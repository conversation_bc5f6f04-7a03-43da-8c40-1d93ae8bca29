# 模型推理测试系统 - 操作手册

### 安装软件
```bash
# 1. 一键安装环境
sudo chmod +x ./setup_environment.sh
sudo ./setup_environment.sh

# 2. 安装.deb包
sudo dpkg -i inference-test-app_1.0.0_arm64.deb

# 3. 修复依赖（如果需要）
sudo apt-get install -f
```

### 使用软件
## 启动软件
```bash
# 命令行启动
sudo inference-test-app

```
## 操作流程
1. **拍照**: 点击"拍照"/"上传"按钮(根据题目内容拍摄相应图片，或上传图片)
2. **推理**: 点击"开始推理"按钮
3. **查看**: 等待结果显示

默认路径
# RK3588推理可执行文件路径
demo_path=/userdata/install/demo_Linux_aarch64/demo（需要chmod +x添加可执行权限）

# RKNN模型文件路径
rknn_model_path=/userdata/models/qwen2_vl_2b_vision_rk3588.rknn

# RKLLM模型文件路径
rkllm_model_path=/userdata/models/Qwen2-2B-vl-Instruct.rkllm

可以在配置文件`/etc/inference-test-app/inference.conf`中修改

# `/opt/inference-test-app/config/questions.json`中修改问题数据（已经给出了题目类型的格式）

---

## 卸载软件

```bash
# 卸载软件
sudo dpkg -r inference-test-app

# 删除配置文件和日志
sudo apt remove inference-test-app

# 完全卸载
sudo apt purge inference-test-app

```
---

## 重要目录

- **程序目录**: `/opt/inference-test-app/`
- **配置文件**: `/etc/inference-test-app/inference.conf`
- **图片保存**: `/opt/inference-test-app/saved_images/`（程序启动时会清空旧图片，重要图片请及时备份）

