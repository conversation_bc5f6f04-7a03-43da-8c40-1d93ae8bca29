#!/bin/bash
# RK3588推理测试脚本

export LD_LIBRARY_PATH="/userdata/install/demo_Linux_aarch64/lib:${LD_LIBRARY_PATH}"

# 不要在错误时立即退出，让脚本继续执行其他推理
set +e

# 检查参数
if [ $# -ne 2 ]; then
    echo "使用方法: $0 <图片路径> <推理次数>"
    exit 1
fi

IMAGE_PATH="$1"
NUM_RUNS="$2"

# 验证推理次数
if ! [[ "$NUM_RUNS" =~ ^[0-9]+$ ]] || [ "$NUM_RUNS" -le 0 ]; then
    echo "错误: 推理次数必须是大于0的整数"
    exit 1
fi

# 检查图片文件
if [ ! -f "$IMAGE_PATH" ]; then
    echo "错误: 图片文件不存在: $IMAGE_PATH"
    exit 1
fi

# 执行单次推理
run_single_inference() {
    local run_index="$1"

    echo "第 $((run_index + 1)) 次推理:"

    # 记录开始时间
    local start_time=$(date +%s.%N)

    # 执行推理命令
    local output
    local exit_code=0

    # 调用demo程序，添加<image>标签到问题文本中
    output=$(/userdata/install/demo_Linux_aarch64/demo \
        "$IMAGE_PATH" \
        "/userdata/models/qwen2_vl_2b_vision_rk3588.rknn" \
        "/userdata/models/Qwen2-2B-vl-Instruct.rkllm" \
        "128" \
        "512" \
        "4" \
        "请评估图片中餐盘内菜肴的剩余情况，你只需输出\"剩余\"或\"未剩余\"。<image>" 2>&1) || exit_code=$?

    # 记录结束时间
    local end_time=$(date +%s.%N)
    local inference_time=$(echo "$end_time - $start_time" | bc -l)

    # 处理结果
    if [ $exit_code -eq 0 ]; then
        printf "  耗时: %.3f秒\n" "$inference_time"
        echo "  输出: $output"
        # 记录到数组
        INFERENCE_TIMES[$run_index]="$inference_time"
        INFERENCE_RESULTS[$run_index]="success"
        return 0
    else
        printf "  耗时: %.3f秒\n" "$inference_time"
        echo "  失败: $output"
        # 记录到数组
        INFERENCE_TIMES[$run_index]="$inference_time"
        INFERENCE_RESULTS[$run_index]="failed"
        return 1
    fi
}

# 主执行逻辑
echo "开始推理测试..."
echo "图片路径: $IMAGE_PATH"
echo "推理次数: $NUM_RUNS"
echo

# 初始化数组
declare -a INFERENCE_TIMES
declare -a INFERENCE_RESULTS

# 执行多次推理
TOTAL_START_TIME=$(date +%s.%N)
SUCCESSFUL_RUNS=0
FAILED_RUNS=0

for ((i=0; i<NUM_RUNS; i++)); do
    if run_single_inference "$i"; then
        ((SUCCESSFUL_RUNS++))
    else
        ((FAILED_RUNS++))
    fi
    echo
done

TOTAL_END_TIME=$(date +%s.%N)
TOTAL_TIME=$(echo "$TOTAL_END_TIME - $TOTAL_START_TIME" | bc -l)

# 统计结果
echo "=== 推理性能统计 ==="
echo "总推理次数: $((SUCCESSFUL_RUNS + FAILED_RUNS))"
echo "成功次数: $SUCCESSFUL_RUNS"
echo "失败次数: $FAILED_RUNS"

if [ $SUCCESSFUL_RUNS -gt 0 ]; then
    # 计算成功推理的时间统计
    SUM=0
    COUNT=0
    MIN_TIME=""
    MAX_TIME=""

    for ((i=0; i<NUM_RUNS; i++)); do
        if [ "${INFERENCE_RESULTS[$i]}" = "success" ]; then
            TIME="${INFERENCE_TIMES[$i]}"
            SUM=$(echo "$SUM + $TIME" | bc -l)
            ((COUNT++))

            if [ -z "$MIN_TIME" ] || [ $(echo "$TIME < $MIN_TIME" | bc -l) -eq 1 ]; then
                MIN_TIME="$TIME"
            fi

            if [ -z "$MAX_TIME" ] || [ $(echo "$TIME > $MAX_TIME" | bc -l) -eq 1 ]; then
                MAX_TIME="$TIME"
            fi
        fi
    done

    AVG_TIME=$(echo "scale=3; $SUM / $COUNT" | bc -l)

    echo
    echo "=== 时间统计 ==="
    printf "总时间: %.3f秒\n" "$TOTAL_TIME"
    printf "平均推理时间: %.3f秒\n" "$AVG_TIME"
    printf "最快推理时间: %.3f秒\n" "$MIN_TIME"
    printf "最慢推理时间: %.3f秒\n" "$MAX_TIME"

    echo
    echo "=== 各次推理时间 ==="
    for ((i=0; i<NUM_RUNS; i++)); do
        if [ "${INFERENCE_RESULTS[$i]}" = "success" ]; then
            printf "第%d次: %.3f秒\n" "$((i+1))" "${INFERENCE_TIMES[$i]}"
        fi
    done
fi
