#ifndef MODELINFERENCE_H
#define MODELINFERENCE_H

#include <QObject>
#include <QThread>
#include <QProcess>
#include <QTimer>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QFileInfo>
#include <QDir>
#include <QDebug>
#include "taskdata.h"
#include "configmanager.h"

/**
 * @brief 模型推理工作线程
 * 在后台线程中执行模型推理，避免阻塞UI
 */
class InferenceWorker : public QObject
{
    Q_OBJECT

public:
    explicit InferenceWorker(QObject *parent = nullptr);
    ~InferenceWorker();

public slots:
    void runInference(const QString& imagePath, const QString& symptomText,
                     const QString& modelPath, const QString& configPath);
    void runRK3588Inference(const QString& imagePath);

signals:
    void inferenceCompleted(const InferenceResult& result);
    void inferenceError(const QString& errorMessage);
    void progressUpdated(int percentage);

private:
    QProcess *m_process;
    QString m_tempDir;
    
    void setupTempDirectory();
    QString createInputJson(const QString& imagePath, const QString& symptomText) const;
    InferenceResult parseOutputJson(const QString& outputPath) const;
    // 输出解析函数已删除 - 直接使用原始输出
    // 模拟结果函数已删除 - 不再提供模拟功能
};

/**
 * @brief 模型推理管理器
 * 管理模型推理的整个生命周期
 */
class ModelInference : public QObject
{
    Q_OBJECT

public:
    explicit ModelInference(QObject *parent = nullptr);
    ~ModelInference();

    // 开始推理
    void startInference(const QString& imagePath, const QString& symptomText,
                       const TaskData& taskData);

    // 开始RK3588模型推理
    void startRK3588Inference(const QString& imagePath);

    // 停止推理
    void stopInference();
    
    // 检查推理状态
    bool isInferencing() const { return m_isInferencing; }
    
    // 设置超时时间（秒）
    void setTimeout(int seconds) { m_timeoutSeconds = seconds; }
    
    // 获取推理进度
    int getProgress() const { return m_progress; }

signals:
    void inferenceStarted();
    void inferenceCompleted(const InferenceResult& result);
    void inferenceError(const QString& errorMessage);
    void progressUpdated(int percentage);
    void timeoutOccurred();

private slots:
    void onInferenceCompleted(const InferenceResult& result);
    void onInferenceError(const QString& errorMessage);
    void onProgressUpdated(int percentage);
    void onTimeout();

private:
    void setupWorkerThread();
    void cleanupWorkerThread();
    bool validateInputs(const QString& imagePath, const QString& symptomText) const;
    QString getModelExecutablePath(const QString& modelPath) const;
    
    // 工作线程
    QThread *m_workerThread;
    InferenceWorker *m_worker;
    
    // 状态变量
    bool m_isInferencing;
    int m_progress;
    int m_timeoutSeconds;
    
    // 定时器
    QTimer *m_timeoutTimer;
    
    // 常量
    static const int DEFAULT_TIMEOUT_SECONDS = 30;
};

#endif // MODELINFERENCE_H
