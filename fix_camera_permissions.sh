#!/bin/bash
# 摄像头权限修复脚本

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
echo_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
echo_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
echo_error() { echo -e "${RED}[ERROR]${NC} $1"; }

echo_info "=== 摄像头权限修复工具 ==="

# 检查当前用户
current_user=$(whoami)
echo_info "当前用户: $current_user"

# 检查video组
if getent group video > /dev/null 2>&1; then
    echo_success "video组存在"
else
    echo_warning "video组不存在，正在创建..."
    sudo groupadd video
    echo_success "video组创建完成"
fi

# 检查用户是否在video组中
if groups "$current_user" | grep -q "\bvideo\b"; then
    echo_success "用户 $current_user 已在 video 组中"
else
    echo_warning "用户 $current_user 不在 video 组中，正在添加..."
    sudo usermod -a -G video "$current_user"
    echo_success "用户已添加到 video 组"
    echo_warning "请重新登录或运行 'newgrp video' 使权限生效"
fi

# 检查摄像头设备
echo_info "检查摄像头设备..."
video_devices=$(ls /dev/video* 2>/dev/null || true)

if [ -z "$video_devices" ]; then
    echo_error "未找到摄像头设备"
    echo_info "请检查："
    echo_info "1. 摄像头是否正确连接"
    echo_info "2. 驱动是否正确安装"
    echo_info "3. 运行 'lsusb' 查看USB设备"
    exit 1
fi

echo_success "找到摄像头设备:"
for device in $video_devices; do
    echo_info "  $device"
    
    # 检查设备权限
    if [ -r "$device" ] && [ -w "$device" ]; then
        echo_success "  ✓ $device 权限正常"
    else
        echo_warning "  ✗ $device 权限不足，正在修复..."
        sudo chmod 666 "$device"
        echo_success "  ✓ $device 权限已修复"
    fi
done

# 测试摄像头
echo_info "测试摄像头访问..."
if command -v ffmpeg > /dev/null 2>&1; then
    test_device="/dev/video0"
    if [ -e "$test_device" ]; then
        echo_info "使用 ffmpeg 测试摄像头..."
        if timeout 5 ffmpeg -f v4l2 -video_size 640x480 -framerate 30 -i "$test_device" -vframes 1 -f null - > /dev/null 2>&1; then
            echo_success "摄像头测试成功！"
        else
            echo_warning "摄像头测试失败，可能的原因："
            echo_info "1. 摄像头被其他程序占用"
            echo_info "2. 摄像头不支持指定的格式"
            echo_info "3. 需要重新登录使权限生效"
        fi
    fi
else
    echo_warning "未安装 ffmpeg，无法测试摄像头"
fi

echo_info "=== 修复完成 ==="
echo_info "如果问题仍然存在，请："
echo_info "1. 重新登录系统"
echo_info "2. 或运行: newgrp video"
echo_info "3. 然后重新测试应用程序"
