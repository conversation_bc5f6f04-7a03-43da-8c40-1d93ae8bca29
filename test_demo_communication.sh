#!/bin/bash
# 测试demo程序的通信方式

echo "测试demo程序通信..."

DEMO_PATH="/userdata/install/demo_Linux_aarch64/demo"
RKNN_MODEL="/userdata/models/qwen2_vl_2b_vision_rk3588.rknn"
RKLLM_MODEL="/userdata/models/Qwen2-2B-vl-Instruct.rkllm"
TEST_IMAGE="/opt/inference-test-app/saved_images/captured_20250723_190539.jpg"

echo "检查文件是否存在..."
if [ ! -f "$DEMO_PATH" ]; then
    echo "错误: Demo程序不存在: $DEMO_PATH"
    exit 1
fi

if [ ! -f "$RKNN_MODEL" ]; then
    echo "错误: RKNN模型不存在: $RKNN_MODEL"
    exit 1
fi

if [ ! -f "$RKLLM_MODEL" ]; then
    echo "错误: RKLLM模型不存在: $RKLLM_MODEL"
    exit 1
fi

if [ ! -f "$TEST_IMAGE" ]; then
    echo "警告: 测试图片不存在: $TEST_IMAGE"
    echo "将使用默认测试图片"
    TEST_IMAGE="/userdata/1.jpeg"
fi

echo "启动demo程序..."
echo "命令: $DEMO_PATH $RKNN_MODEL $RKLLM_MODEL 128 512 4"

# 使用expect来自动化交互
expect << EOF
spawn $DEMO_PATH $RKNN_MODEL $RKLLM_MODEL 128 512 4
expect "user:" {
    send "$TEST_IMAGE <image>测试图片内容\r"
    expect "robot:" {
        puts "收到robot响应"
        expect "user:" {
            send "exit\r"
        }
    }
}
expect eof
EOF

echo "测试完成"
