# 模型推理测试系统部署指南

## 概述

本指南提供了在arm64 Debian系统上部署模型推理测试系统的完整流程。

## 部署方式

### 方式一：环境检查 + 定制安装（推荐）

适用于需要详细了解系统状态的情况。

#### 步骤1：运行环境检查

```bash
# 下载检查脚本到目标设备
chmod +x check_environment.sh
./check_environment.sh
```

检查完成后会生成 `environment_check_report.txt` 报告文件。

#### 步骤2：发送报告

将生成的报告文件发送给开发人员，我们会根据报告内容提供定制的安装脚本。

#### 步骤3：执行定制安装脚本

根据收到的定制脚本进行安装。

### 方式二：一键式安装

适用于标准环境的快速部署。

```bash
# 下载安装脚本到目标设备
chmod +x setup_environment.sh
sudo ./setup_environment.sh
```

## 检查脚本功能

`check_environment.sh` 会检查以下项目：

### 系统要求
- ✅ 系统架构（必须是aarch64）
- ✅ 操作系统（推荐Debian）

### 开发环境
- ✅ Qt5开发包（qtbase5-dev, qtmultimedia5-dev）
- ✅ 编译工具链（gcc, g++, make, pkg-config）
- ✅ qmake工具

### 多媒体支持
- ✅ FFmpeg（摄像头测试必需）
- ⚠️ V4L2工具（摄像头管理，建议安装）

### 目录结构
- ✅ /userdata目录及权限
- ✅ 推理模型目录 /userdata/install/demo_Linux_aarch64/
- ✅ 推理程序 demo 可执行文件

### 硬件设备
- ⚠️ 摄像头设备（/dev/video*）
- ⚠️ 网络连接

## 安装脚本功能

`setup_environment.sh` 会自动安装：

### 基础工具
- build-essential
- pkg-config
- cmake
- git

### Qt5开发环境
- qtbase5-dev
- qtmultimedia5-dev
- qttools5-dev
- qttools5-dev-tools
- libqt5multimedia5-plugins
- libqt5multimediawidgets5

### 多媒体工具
- ffmpeg
- v4l-utils
- libv4l-dev
- libasound2-dev
- libpulse-dev

### 目录结构
- /userdata/
- /userdata/install/
- /userdata/install/demo_Linux_aarch64/
- /opt/inference-test-app/
- /etc/inference-test-app/

### 权限配置
- 用户添加到video组
- 目录权限设置

## 部署后步骤

### 1. 部署推理模型

将推理模型文件复制到目标目录：

```bash
# 示例：复制推理模型
sudo cp demo /userdata/install/demo_Linux_aarch64/
sudo cp *.rknn /userdata/install/demo_Linux_aarch64/
sudo cp *.rkllm /userdata/install/demo_Linux_aarch64/
sudo chmod +x /userdata/install/demo_Linux_aarch64/demo
```

### 2. 安装应用程序

```bash
# 安装.deb包
sudo dpkg -i inference-test-app_1.0.0_arm64.deb

# 如果有依赖问题，运行：
sudo apt-get install -f
```

### 3. 配置应用程序

编辑配置文件：

```bash
sudo nano /etc/inference-test-app/inference.conf
```

根据实际部署情况调整路径和参数。

### 4. 测试运行

```bash
# 命令行启动
inference-test-app

# 或直接运行
/opt/inference-test-app/run_inference_app.sh
```

## 故障排除

### 常见问题

#### 1. 权限问题
```bash
# 如果遇到摄像头权限问题
sudo usermod -a -G video $USER
# 重新登录生效
```

#### 2. 库文件缺失
```bash
# 检查库文件依赖
ldd /opt/inference-test-app/InferenceApp

# 安装缺失的库
sudo apt-get install <missing-package>
```

#### 3. 推理程序不可执行
```bash
# 设置执行权限
sudo chmod +x /userdata/install/demo_Linux_aarch64/demo

# 检查文件类型
file /userdata/install/demo_Linux_aarch64/demo
```

#### 4. 摄像头设备未找到
```bash
# 检查摄像头设备
ls /dev/video*

# 检查USB摄像头
lsusb

# 加载摄像头驱动
sudo modprobe uvcvideo
```

### 日志文件

- 环境检查报告：`environment_check_report.txt`
- 安装日志：`environment_setup.log`
- 应用程序日志：查看应用程序输出

## 联系支持

如果遇到问题，请提供以下信息：

1. 环境检查报告文件
2. 安装日志文件
3. 错误信息截图
4. 系统信息：`uname -a` 和 `lsb_release -a`

## 版本兼容性

- **系统架构**：arm64 (aarch64)
- **操作系统**：Debian 10+ 或 Ubuntu 18.04+
- **Qt版本**：Qt 5.12+
- **GCC版本**：GCC 7+

## 安全注意事项

- 安装脚本需要root权限
- 确保从可信来源下载脚本
- 在生产环境部署前先在测试环境验证
- 定期更新系统和依赖包
