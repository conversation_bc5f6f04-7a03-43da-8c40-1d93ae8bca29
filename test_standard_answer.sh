#!/bin/bash
# 测试标准答案显示功能

echo "=== 测试标准答案显示功能 ==="

echo "1. 检查onInferenceCompleted函数修改:"
if grep -q "标准答案：" taskpage.cpp; then
    echo "  ✓ 已添加标准答案显示逻辑"
else
    echo "  ✗ 未找到标准答案显示逻辑"
fi

echo ""
echo "2. 检查模拟结果格式:"
if grep -q "分析：" taskpage.cpp && grep -q "答案：" taskpage.cpp; then
    echo "  ✓ 模拟结果已使用正确格式"
else
    echo "  ✗ 模拟结果格式不正确"
fi

echo ""
echo "3. 检查题目答案获取:"
if grep -q "question.answer" taskpage.cpp; then
    echo "  ✓ 已添加题目答案获取逻辑"
else
    echo "  ✗ 未添加题目答案获取逻辑"
fi

echo ""
echo "4. 检查配置文件中的题目答案:"
if [ -f "config/questions.json" ]; then
    answer_count=$(grep -c '"A":' config/questions.json || echo "0")
    echo "  题目文件中的答案数量: $answer_count"
    if [ "$answer_count" -gt 0 ]; then
        echo "  ✓ 题目文件包含答案"
    else
        echo "  ✗ 题目文件不包含答案"
    fi
else
    echo "  ✗ 题目文件不存在"
fi

echo ""
echo "=== 预期效果 ==="
echo "推理完成后，结果区域应显示："
echo "分析："
echo "[模型的分析内容]"
echo ""
echo "答案："
echo "[模型给出的答案]"
echo ""
echo "标准答案：[正确答案]"

echo ""
echo "=== 测试完成 ==="
