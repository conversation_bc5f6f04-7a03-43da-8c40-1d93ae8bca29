#!/bin/bash
# 卸载后脚本

set -e

case "$1" in
    remove|purge)
        # 询问是否删除配置文件和日志
        if [ "$1" = "purge" ]; then
            echo "清理配置文件和日志..."
            rm -rf /etc/inference-test-app
            rm -rf /var/log/inference-test-app
            echo "配置文件和日志已清理"
        else
            echo "配置文件保留在 /etc/inference-test-app"
            echo "日志文件保留在 /var/log/inference-test-app"
            echo "如需完全清理，请运行: sudo apt purge inference-test-app"
        fi
        ;;
    *)
        ;;
esac

echo "模型推理测试系统卸载完成！"

exit 0
