#############################################################################
# Makefile for building: DiagnosisApp
# Generated by qmake (3.1) (Qt 5.12.6)
# Project:  ../DiagnosisApp.pro
# Template: app
# Command: /opt/Qt5.12.6/5.12.6/gcc_64/bin/qmake -o Makefile ../DiagnosisApp.pro
#############################################################################

MAKEFILE      = Makefile

EQ            = =

####### Compiler, tools and options

CC            = gcc
CXX           = g++
DEFINES       = -DQT_DEPRECATED_WARNINGS -DRELEASE_MODE -DQT_NO_DEBUG -DQT_MULTIMEDIAWIDGETS_LIB -DQT_WIDGETS_LIB -DQT_MULTIMEDIA_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB
CFLAGS        = -pipe -O2 -Wall -W -D_REENTRANT -fPIC $(DEFINES)
CXXFLAGS      = -pipe -fno-sized-deallocation -O2 -O2 -std=gnu++1z -Wall -W -D_REENTRANT -fPIC $(DEFINES)
INCPATH       = -I../../diagnosis -I. -I../../diagnosis -I/opt/Qt5.12.6/5.12.6/gcc_64/include -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimediaWidgets -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore -Ibuild/moc -isystem /usr/include/libdrm -I/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/linux-g++
QMAKE         = /opt/Qt5.12.6/5.12.6/gcc_64/bin/qmake
DEL_FILE      = rm -f
CHK_DIR_EXISTS= test -d
MKDIR         = mkdir -p
COPY          = cp -f
COPY_FILE     = cp -f
COPY_DIR      = cp -f -R
INSTALL_FILE  = install -m 644 -p
INSTALL_PROGRAM = install -m 755 -p
INSTALL_DIR   = cp -f -R
QINSTALL      = /opt/Qt5.12.6/5.12.6/gcc_64/bin/qmake -install qinstall
QINSTALL_PROGRAM = /opt/Qt5.12.6/5.12.6/gcc_64/bin/qmake -install qinstall -exe
DEL_FILE      = rm -f
SYMLINK       = ln -f -s
DEL_DIR       = rmdir
MOVE          = mv -f
TAR           = tar -cf
COMPRESS      = gzip -9f
DISTNAME      = DiagnosisApp1.0.0
DISTDIR = /mnt/e/code/diagnosis/build/build/obj/DiagnosisApp1.0.0
LINK          = g++
LFLAGS        = -Wl,-O1 -Wl,-rpath,/opt/Qt5.12.6/5.12.6/gcc_64/lib -Wl,-rpath-link,/opt/Qt5.12.6/5.12.6/gcc_64/lib
LIBS          = $(SUBLIBS) /opt/Qt5.12.6/5.12.6/gcc_64/lib/libQt5MultimediaWidgets.so /opt/Qt5.12.6/5.12.6/gcc_64/lib/libQt5Widgets.so /opt/Qt5.12.6/5.12.6/gcc_64/lib/libQt5Multimedia.so /opt/Qt5.12.6/5.12.6/gcc_64/lib/libQt5Gui.so /opt/Qt5.12.6/5.12.6/gcc_64/lib/libQt5Network.so /opt/Qt5.12.6/5.12.6/gcc_64/lib/libQt5Core.so -lGL -lpthread   
AR            = ar cqs
RANLIB        = 
SED           = sed
STRIP         = strip

####### Output directory

OBJECTS_DIR   = build/obj/

####### Files

SOURCES       = ../main.cpp \
		../mainwindow.cpp \
		../taskpage.cpp \
		../taskmanager.cpp \
		../modelinference.cpp \
		../camerawidget.cpp build/rcc/qrc_DiagnosisApp.cpp \
		build/moc/moc_mainwindow.cpp \
		build/moc/moc_taskpage.cpp \
		build/moc/moc_taskmanager.cpp \
		build/moc/moc_modelinference.cpp \
		build/moc/moc_camerawidget.cpp
OBJECTS       = build/obj/main.o \
		build/obj/mainwindow.o \
		build/obj/taskpage.o \
		build/obj/taskmanager.o \
		build/obj/modelinference.o \
		build/obj/camerawidget.o \
		build/obj/qrc_DiagnosisApp.o \
		build/obj/moc_mainwindow.o \
		build/obj/moc_taskpage.o \
		build/obj/moc_taskmanager.o \
		build/obj/moc_modelinference.o \
		build/obj/moc_camerawidget.o
DIST          = /opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/spec_pre.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/common/unix.conf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/common/linux.conf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/common/sanitize.conf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/common/gcc-base.conf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/common/gcc-base-unix.conf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/common/g++-base.conf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/common/g++-unix.conf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/qconfig.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3danimation.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3danimation_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dcore.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dcore_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dextras.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dextras_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dinput.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dinput_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dlogic.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dlogic_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquick.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquick_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickanimation.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickanimation_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickextras.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickextras_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickinput.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickinput_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickrender.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickrender_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickscene2d.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickscene2d_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3drender.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3drender_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_accessibility_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_bluetooth.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_bluetooth_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_bootstrap_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_charts.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_charts_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_concurrent.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_concurrent_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_core.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_core_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_datavisualization.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_datavisualization_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_dbus.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_dbus_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_designer.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_designer_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_designercomponents_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_devicediscovery_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_edid_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_egl_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_eglfs_kms_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_eglfsdeviceintegration_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_eventdispatcher_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_fb_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_fontdatabase_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_gamepad.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_gamepad_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_glx_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_gui.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_gui_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_help.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_help_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_input_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_kms_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_linuxaccessibility_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_location.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_location_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_multimedia.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_multimedia_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_multimediagsttools_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_multimediawidgets.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_multimediawidgets_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_network.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_network_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_networkauth.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_networkauth_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_nfc.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_nfc_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_opengl.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_opengl_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_openglextensions.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_openglextensions_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_packetprotocol_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_platformcompositor_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_positioning.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_positioning_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_positioningquick.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_positioningquick_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_printsupport.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_printsupport_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_purchasing.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_purchasing_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_qml.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_qml_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_qmldebug_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_qmldevtools_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_qmltest.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_qmltest_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_qtmultimediaquicktools_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quick.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quick_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quickcontrols2.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quickcontrols2_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quickparticles_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quickshapes_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quicktemplates2.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quicktemplates2_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quickwidgets.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quickwidgets_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_remoteobjects.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_remoteobjects_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_repparser.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_repparser_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_script.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_script_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_scripttools.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_scripttools_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_scxml.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_scxml_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_sensors.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_sensors_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_serialbus.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_serialbus_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_serialport.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_serialport_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_service_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_sql.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_sql_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_svg.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_svg_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_testlib.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_testlib_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_texttospeech.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_texttospeech_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_theme_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_uiplugin.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_uitools.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_uitools_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_virtualkeyboard.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_virtualkeyboard_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_vulkan_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_waylandclient.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_waylandclient_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_waylandcompositor.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_waylandcompositor_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webchannel.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webchannel_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webengine.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webengine_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webenginecore.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webenginecore_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webenginecoreheaders_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webenginewidgets.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webenginewidgets_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_websockets.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_websockets_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webview.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webview_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_widgets.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_widgets_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_x11extras.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_x11extras_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_xcb_qpa_lib_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_xml.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_xml_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_xmlpatterns.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_xmlpatterns_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/qt_functions.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/qt_config.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/linux-g++/qmake.conf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/spec_post.prf \
		../.qmake.stash \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/exclusive_builds.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/toolchain.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/default_pre.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/resolve_config.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/default_post.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/warn_on.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/qt.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/resources.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/moc.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/unix/opengl.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/uic.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/unix/thread.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/qmake_use.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/file_copies.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/testcase_targets.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/exceptions.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/yacc.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/lex.prf \
		../../DiagnosisApp.pro ../mainwindow.h \
		../taskpage.h \
		../taskmanager.h \
		../modelinference.h \
		../camerawidget.h \
		../taskdata.h ../main.cpp \
		../mainwindow.cpp \
		../taskpage.cpp \
		../taskmanager.cpp \
		../modelinference.cpp \
		../camerawidget.cpp
QMAKE_TARGET  = DiagnosisApp
DESTDIR       = 
TARGET        = DiagnosisApp


first: all
####### Build rules

DiagnosisApp:  $(OBJECTS)  
	$(LINK) $(LFLAGS) -o $(TARGET) $(OBJECTS) $(OBJCOMP) $(LIBS)

Makefile: ../DiagnosisApp.pro /opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/linux-g++/qmake.conf /opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/spec_pre.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/common/unix.conf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/common/linux.conf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/common/sanitize.conf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/common/gcc-base.conf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/common/gcc-base-unix.conf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/common/g++-base.conf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/common/g++-unix.conf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/qconfig.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3danimation.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3danimation_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dcore.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dcore_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dextras.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dextras_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dinput.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dinput_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dlogic.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dlogic_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquick.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquick_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickanimation.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickanimation_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickextras.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickextras_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickinput.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickinput_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickrender.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickrender_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickscene2d.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickscene2d_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3drender.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3drender_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_accessibility_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_bluetooth.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_bluetooth_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_bootstrap_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_charts.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_charts_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_concurrent.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_concurrent_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_core.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_core_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_datavisualization.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_datavisualization_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_dbus.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_dbus_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_designer.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_designer_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_designercomponents_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_devicediscovery_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_edid_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_egl_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_eglfs_kms_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_eglfsdeviceintegration_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_eventdispatcher_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_fb_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_fontdatabase_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_gamepad.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_gamepad_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_glx_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_gui.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_gui_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_help.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_help_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_input_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_kms_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_linuxaccessibility_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_location.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_location_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_multimedia.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_multimedia_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_multimediagsttools_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_multimediawidgets.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_multimediawidgets_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_network.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_network_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_networkauth.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_networkauth_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_nfc.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_nfc_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_opengl.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_opengl_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_openglextensions.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_openglextensions_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_packetprotocol_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_platformcompositor_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_positioning.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_positioning_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_positioningquick.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_positioningquick_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_printsupport.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_printsupport_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_purchasing.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_purchasing_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_qml.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_qml_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_qmldebug_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_qmldevtools_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_qmltest.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_qmltest_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_qtmultimediaquicktools_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quick.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quick_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quickcontrols2.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quickcontrols2_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quickparticles_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quickshapes_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quicktemplates2.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quicktemplates2_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quickwidgets.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quickwidgets_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_remoteobjects.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_remoteobjects_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_repparser.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_repparser_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_script.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_script_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_scripttools.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_scripttools_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_scxml.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_scxml_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_sensors.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_sensors_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_serialbus.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_serialbus_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_serialport.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_serialport_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_service_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_sql.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_sql_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_svg.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_svg_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_testlib.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_testlib_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_texttospeech.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_texttospeech_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_theme_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_uiplugin.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_uitools.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_uitools_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_virtualkeyboard.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_virtualkeyboard_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_vulkan_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_waylandclient.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_waylandclient_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_waylandcompositor.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_waylandcompositor_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webchannel.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webchannel_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webengine.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webengine_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webenginecore.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webenginecore_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webenginecoreheaders_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webenginewidgets.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webenginewidgets_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_websockets.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_websockets_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webview.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webview_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_widgets.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_widgets_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_x11extras.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_x11extras_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_xcb_qpa_lib_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_xml.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_xml_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_xmlpatterns.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_xmlpatterns_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/qt_functions.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/qt_config.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/linux-g++/qmake.conf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/spec_post.prf \
		.qmake.stash \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/exclusive_builds.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/toolchain.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/default_pre.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/resolve_config.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/default_post.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/warn_on.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/qt.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/resources.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/moc.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/unix/opengl.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/uic.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/unix/thread.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/qmake_use.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/file_copies.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/testcase_targets.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/exceptions.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/yacc.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/lex.prf \
		../DiagnosisApp.pro \
		../DiagnosisApp.qrc
	$(QMAKE) -o Makefile ../DiagnosisApp.pro
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/spec_pre.prf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/common/unix.conf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/common/linux.conf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/common/sanitize.conf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/common/gcc-base.conf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/common/gcc-base-unix.conf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/common/g++-base.conf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/common/g++-unix.conf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/qconfig.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3danimation.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3danimation_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dcore.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dcore_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dextras.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dextras_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dinput.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dinput_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dlogic.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dlogic_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquick.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquick_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickanimation.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickanimation_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickextras.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickextras_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickinput.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickinput_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickrender.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickrender_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickscene2d.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickscene2d_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3drender.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3drender_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_accessibility_support_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_bluetooth.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_bluetooth_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_bootstrap_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_charts.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_charts_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_concurrent.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_concurrent_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_core.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_core_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_datavisualization.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_datavisualization_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_dbus.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_dbus_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_designer.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_designer_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_designercomponents_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_devicediscovery_support_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_edid_support_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_egl_support_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_eglfs_kms_support_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_eglfsdeviceintegration_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_eventdispatcher_support_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_fb_support_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_fontdatabase_support_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_gamepad.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_gamepad_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_glx_support_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_gui.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_gui_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_help.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_help_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_input_support_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_kms_support_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_linuxaccessibility_support_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_location.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_location_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_multimedia.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_multimedia_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_multimediagsttools_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_multimediawidgets.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_multimediawidgets_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_network.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_network_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_networkauth.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_networkauth_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_nfc.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_nfc_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_opengl.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_opengl_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_openglextensions.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_openglextensions_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_packetprotocol_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_platformcompositor_support_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_positioning.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_positioning_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_positioningquick.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_positioningquick_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_printsupport.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_printsupport_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_purchasing.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_purchasing_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_qml.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_qml_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_qmldebug_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_qmldevtools_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_qmltest.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_qmltest_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_qtmultimediaquicktools_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quick.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quick_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quickcontrols2.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quickcontrols2_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quickparticles_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quickshapes_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quicktemplates2.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quicktemplates2_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quickwidgets.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quickwidgets_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_remoteobjects.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_remoteobjects_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_repparser.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_repparser_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_script.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_script_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_scripttools.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_scripttools_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_scxml.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_scxml_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_sensors.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_sensors_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_serialbus.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_serialbus_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_serialport.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_serialport_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_service_support_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_sql.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_sql_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_svg.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_svg_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_testlib.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_testlib_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_texttospeech.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_texttospeech_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_theme_support_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_uiplugin.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_uitools.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_uitools_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_virtualkeyboard.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_virtualkeyboard_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_vulkan_support_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_waylandclient.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_waylandclient_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_waylandcompositor.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_waylandcompositor_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webchannel.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webchannel_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webengine.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webengine_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webenginecore.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webenginecore_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webenginecoreheaders_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webenginewidgets.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webenginewidgets_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_websockets.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_websockets_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webview.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webview_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_widgets.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_widgets_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_x11extras.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_x11extras_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_xcb_qpa_lib_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_xml.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_xml_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_xmlpatterns.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_xmlpatterns_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/qt_functions.prf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/qt_config.prf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/linux-g++/qmake.conf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/spec_post.prf:
.qmake.stash:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/exclusive_builds.prf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/toolchain.prf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/default_pre.prf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/resolve_config.prf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/default_post.prf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/warn_on.prf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/qt.prf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/resources.prf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/moc.prf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/unix/opengl.prf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/uic.prf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/unix/thread.prf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/qmake_use.prf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/file_copies.prf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/testcase_targets.prf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/exceptions.prf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/yacc.prf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/lex.prf:
../DiagnosisApp.pro:
../DiagnosisApp.qrc:
qmake: FORCE
	@$(QMAKE) -o Makefile ../DiagnosisApp.pro

qmake_all: FORCE


all: Makefile DiagnosisApp

dist: distdir FORCE
	(cd `dirname $(DISTDIR)` && $(TAR) $(DISTNAME).tar $(DISTNAME) && $(COMPRESS) $(DISTNAME).tar) && $(MOVE) `dirname $(DISTDIR)`/$(DISTNAME).tar.gz . && $(DEL_FILE) -r $(DISTDIR)

distdir: FORCE
	@test -d $(DISTDIR) || mkdir -p $(DISTDIR)
	$(COPY_FILE) --parents $(DIST) $(DISTDIR)/
	$(COPY_FILE) --parents ../DiagnosisApp.qrc $(DISTDIR)/
	$(COPY_FILE) --parents /opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/data/dummy.cpp $(DISTDIR)/
	$(COPY_FILE) --parents ../mainwindow.h ../taskpage.h ../taskmanager.h ../modelinference.h ../camerawidget.h ../taskdata.h $(DISTDIR)/
	$(COPY_FILE) --parents ../main.cpp ../mainwindow.cpp ../taskpage.cpp ../taskmanager.cpp ../modelinference.cpp ../camerawidget.cpp $(DISTDIR)/


clean: compiler_clean 
	-$(DEL_FILE) $(OBJECTS)
	-$(DEL_FILE) *~ core *.core


distclean: clean 
	-$(DEL_FILE) $(TARGET) 
	-$(DEL_FILE) .qmake.stash
	-$(DEL_FILE) Makefile


####### Sub-libraries

mocclean: compiler_moc_header_clean compiler_moc_objc_header_clean compiler_moc_source_clean

mocables: compiler_moc_header_make_all compiler_moc_objc_header_make_all compiler_moc_source_make_all

check: first

benchmark: first

compiler_rcc_make_all: build/rcc/qrc_DiagnosisApp.cpp
compiler_rcc_clean:
	-$(DEL_FILE) build/rcc/qrc_DiagnosisApp.cpp
build/rcc/qrc_DiagnosisApp.cpp: ../DiagnosisApp.qrc \
		/opt/Qt5.12.6/5.12.6/gcc_64/bin/rcc \
		../config/default_tasks.json
	/opt/Qt5.12.6/5.12.6/gcc_64/bin/rcc -name DiagnosisApp ../DiagnosisApp.qrc -o build/rcc/qrc_DiagnosisApp.cpp

compiler_moc_predefs_make_all: build/moc/moc_predefs.h
compiler_moc_predefs_clean:
	-$(DEL_FILE) build/moc/moc_predefs.h
build/moc/moc_predefs.h: /opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/data/dummy.cpp
	g++ -pipe -fno-sized-deallocation -O2 -O2 -std=gnu++1z -Wall -W -dM -E -o build/moc/moc_predefs.h /opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/data/dummy.cpp

compiler_moc_header_make_all: build/moc/moc_mainwindow.cpp build/moc/moc_taskpage.cpp build/moc/moc_taskmanager.cpp build/moc/moc_modelinference.cpp build/moc/moc_camerawidget.cpp
compiler_moc_header_clean:
	-$(DEL_FILE) build/moc/moc_mainwindow.cpp build/moc/moc_taskpage.cpp build/moc/moc_taskmanager.cpp build/moc/moc_modelinference.cpp build/moc/moc_camerawidget.cpp
build/moc/moc_mainwindow.cpp: ../mainwindow.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QMainWindow \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qmainwindow.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qtwidgetsglobal.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtguiglobal.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qglobal.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qconfig.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtcore-config.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsystemdetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qprocessordetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcompilerdetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtypeinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsysinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlogging.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qflags.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbasicatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qgenericatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_cxx11.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_msvc.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qglobalstatic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmutex.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qnumeric.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qversiontagging.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtgui-config.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qtwidgets-config.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qwidget.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qwindowdefs.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobjectdefs.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qnamespace.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qwindowdefs_win.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobject.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstring.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qchar.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbytearray.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qrefcount.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qarraydata.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringliteral.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringalgorithms.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringview.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringbuilder.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qalgorithms.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qiterator.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qhashfunctions.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qpair.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbytearraylist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringlist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qregexp.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringmatcher.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcoreevent.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qscopedpointer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmetatype.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvarlengtharray.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcontainerfwd.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobject_impl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmargins.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpaintdevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qrect.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsize.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qpoint.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpalette.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qcolor.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qrgb.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qrgba64.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qbrush.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvector.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qmatrix.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpolygon.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qregion.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdatastream.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qiodevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qline.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtransform.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpainterpath.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qimage.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpixelformat.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpixmap.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsharedpointer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qshareddata.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qhash.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qfont.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qfontmetrics.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qfontinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qsizepolicy.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qcursor.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qkeysequence.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qevent.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariant.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmap.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdebug.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtextstream.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlocale.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qset.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcontiguouscache.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qurl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qurlquery.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfile.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfiledevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qvector2d.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtouchdevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qtabwidget.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qicon.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QStackedWidget \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstackedwidget.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qframe.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QVBoxLayout \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qboxlayout.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlayout.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlayoutitem.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qgridlayout.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QHBoxLayout \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QPushButton \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qpushbutton.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractbutton.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QLabel \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlabel.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QProgressBar \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qprogressbar.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QStatusBar \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstatusbar.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QMenuBar \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qmenubar.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qmenu.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qaction.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qactiongroup.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QAction \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QTimer \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtimer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbasictimer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/QKeyEvent \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/QCloseEvent \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QMessageBox \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qmessagebox.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qdialog.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QDebug \
		build/moc/moc_predefs.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/bin/moc
	/opt/Qt5.12.6/5.12.6/gcc_64/bin/moc $(DEFINES) --include /mnt/e/code/diagnosis/build/build/moc/moc_predefs.h -I/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/linux-g++ -I/mnt/e/code/diagnosis -I/mnt/e/code/diagnosis -I/opt/Qt5.12.6/5.12.6/gcc_64/include -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimediaWidgets -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore -I. -I/usr/include/c++/9 -I/usr/include/x86_64-linux-gnu/c++/9 -I/usr/include/c++/9/backward -I/usr/lib/gcc/x86_64-linux-gnu/9/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include ../mainwindow.h -o build/moc/moc_mainwindow.cpp

build/moc/moc_taskpage.cpp: ../taskpage.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QWidget \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qwidget.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qtwidgetsglobal.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtguiglobal.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qglobal.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qconfig.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtcore-config.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsystemdetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qprocessordetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcompilerdetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtypeinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsysinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlogging.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qflags.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbasicatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qgenericatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_cxx11.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_msvc.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qglobalstatic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmutex.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qnumeric.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qversiontagging.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtgui-config.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qtwidgets-config.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qwindowdefs.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobjectdefs.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qnamespace.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qwindowdefs_win.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobject.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstring.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qchar.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbytearray.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qrefcount.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qarraydata.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringliteral.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringalgorithms.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringview.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringbuilder.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qalgorithms.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qiterator.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qhashfunctions.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qpair.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbytearraylist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringlist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qregexp.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringmatcher.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcoreevent.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qscopedpointer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmetatype.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvarlengtharray.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcontainerfwd.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobject_impl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmargins.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpaintdevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qrect.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsize.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qpoint.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpalette.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qcolor.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qrgb.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qrgba64.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qbrush.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvector.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qmatrix.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpolygon.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qregion.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdatastream.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qiodevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qline.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtransform.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpainterpath.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qimage.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpixelformat.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpixmap.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsharedpointer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qshareddata.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qhash.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qfont.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qfontmetrics.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qfontinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qsizepolicy.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qcursor.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qkeysequence.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qevent.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariant.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmap.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdebug.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtextstream.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlocale.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qset.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcontiguouscache.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qurl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qurlquery.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfile.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfiledevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qvector2d.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtouchdevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QVBoxLayout \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qboxlayout.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlayout.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlayoutitem.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qgridlayout.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QHBoxLayout \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QGridLayout \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QLabel \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlabel.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qframe.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QPushButton \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qpushbutton.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractbutton.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qicon.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QTextEdit \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qtextedit.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractscrollarea.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtextdocument.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtextoption.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtextcursor.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtextformat.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpen.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QLineEdit \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlineedit.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QFrame \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QScrollArea \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qscrollarea.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QProgressBar \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qprogressbar.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QTimer \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtimer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbasictimer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QFileDialog \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qfiledialog.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdir.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfileinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qdialog.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/QPixmap \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/QMovie \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qmovie.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qimagereader.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcoreapplication.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qeventloop.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qimageiohandler.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qplugin.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qpointer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qjsonobject.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qjsonvalue.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfactoryinterface.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/QDragEnterEvent \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/QDropEvent \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QMimeData \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmimedata.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QUrl \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QDebug \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QJsonDocument \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qjsondocument.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QJsonArray \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qjsonarray.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QJsonObject \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QFile \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QRadioButton \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qradiobutton.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QButtonGroup \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qbuttongroup.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QGroupBox \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qgroupbox.h \
		../taskdata.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QString \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QStringList \
		../camerawidget.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QComboBox \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qcombobox.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractitemdelegate.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractspinbox.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qvalidator.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qregularexpression.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qslider.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractslider.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyle.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qtabbar.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qtabwidget.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qrubberband.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qabstractitemmodel.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/QCamera \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qcamera.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qmediacontrol.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qtmultimediaglobal.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qtmultimedia-config.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qmediaobject.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qmultimedia.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qmediaservice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qcameraexposure.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qmediaenumdebug.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmetaobject.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qcamerafocus.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qcameraimageprocessing.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qcameraviewfindersettings.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qvideoframe.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qabstractvideobuffer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimediaWidgets/QCameraViewfinder \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimediaWidgets/qcameraviewfinder.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimediaWidgets/qvideowidget.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimediaWidgets/qtmultimediawidgetdefs.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qmediabindableinterface.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/QCameraImageCapture \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qcameraimagecapture.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qmediaencodersettings.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/QCameraInfo \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qcamerainfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/QImageEncoderSettings \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QStandardPaths \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstandardpaths.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QDir \
		../modelinference.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QObject \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QThread \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qthread.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QProcess \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qprocess.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QFileInfo \
		build/moc/moc_predefs.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/bin/moc
	/opt/Qt5.12.6/5.12.6/gcc_64/bin/moc $(DEFINES) --include /mnt/e/code/diagnosis/build/build/moc/moc_predefs.h -I/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/linux-g++ -I/mnt/e/code/diagnosis -I/mnt/e/code/diagnosis -I/opt/Qt5.12.6/5.12.6/gcc_64/include -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimediaWidgets -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore -I. -I/usr/include/c++/9 -I/usr/include/x86_64-linux-gnu/c++/9 -I/usr/include/c++/9/backward -I/usr/lib/gcc/x86_64-linux-gnu/9/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include ../taskpage.h -o build/moc/moc_taskpage.cpp

build/moc/moc_taskmanager.cpp: ../taskmanager.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QObject \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobject.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobjectdefs.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qnamespace.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qglobal.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qconfig.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtcore-config.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsystemdetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qprocessordetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcompilerdetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtypeinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsysinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlogging.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qflags.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbasicatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qgenericatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_cxx11.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_msvc.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qglobalstatic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmutex.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qnumeric.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qversiontagging.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstring.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qchar.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbytearray.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qrefcount.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qarraydata.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringliteral.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringalgorithms.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringview.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringbuilder.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qalgorithms.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qiterator.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qhashfunctions.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qpair.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbytearraylist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringlist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qregexp.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringmatcher.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcoreevent.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qscopedpointer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmetatype.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvarlengtharray.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcontainerfwd.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobject_impl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QJsonDocument \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qjsondocument.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qjsonvalue.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QJsonObject \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qjsonobject.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QJsonArray \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qjsonarray.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QFile \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfile.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfiledevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qiodevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QDir \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdir.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfileinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qshareddata.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qhash.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QStandardPaths \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstandardpaths.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QDebug \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdebug.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmap.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtextstream.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlocale.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariant.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvector.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qpoint.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qset.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcontiguouscache.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsharedpointer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsharedpointer_impl.h \
		../taskdata.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QString \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QStringList \
		build/moc/moc_predefs.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/bin/moc
	/opt/Qt5.12.6/5.12.6/gcc_64/bin/moc $(DEFINES) --include /mnt/e/code/diagnosis/build/build/moc/moc_predefs.h -I/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/linux-g++ -I/mnt/e/code/diagnosis -I/mnt/e/code/diagnosis -I/opt/Qt5.12.6/5.12.6/gcc_64/include -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimediaWidgets -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore -I. -I/usr/include/c++/9 -I/usr/include/x86_64-linux-gnu/c++/9 -I/usr/include/c++/9/backward -I/usr/lib/gcc/x86_64-linux-gnu/9/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include ../taskmanager.h -o build/moc/moc_taskmanager.cpp

build/moc/moc_modelinference.cpp: ../modelinference.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QObject \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobject.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobjectdefs.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qnamespace.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qglobal.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qconfig.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtcore-config.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsystemdetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qprocessordetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcompilerdetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtypeinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsysinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlogging.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qflags.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbasicatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qgenericatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_cxx11.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_msvc.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qglobalstatic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmutex.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qnumeric.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qversiontagging.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstring.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qchar.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbytearray.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qrefcount.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qarraydata.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringliteral.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringalgorithms.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringview.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringbuilder.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qalgorithms.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qiterator.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qhashfunctions.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qpair.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbytearraylist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringlist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qregexp.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringmatcher.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcoreevent.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qscopedpointer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmetatype.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvarlengtharray.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcontainerfwd.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobject_impl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QThread \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qthread.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QProcess \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qprocess.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qiodevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qshareddata.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qhash.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QTimer \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtimer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbasictimer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QJsonDocument \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qjsondocument.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qjsonvalue.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QJsonObject \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qjsonobject.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QJsonArray \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qjsonarray.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QFileInfo \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfileinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfile.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfiledevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QDir \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdir.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QDebug \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdebug.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmap.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtextstream.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlocale.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariant.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvector.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qpoint.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qset.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcontiguouscache.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsharedpointer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsharedpointer_impl.h \
		../taskdata.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QString \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QStringList \
		build/moc/moc_predefs.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/bin/moc
	/opt/Qt5.12.6/5.12.6/gcc_64/bin/moc $(DEFINES) --include /mnt/e/code/diagnosis/build/build/moc/moc_predefs.h -I/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/linux-g++ -I/mnt/e/code/diagnosis -I/mnt/e/code/diagnosis -I/opt/Qt5.12.6/5.12.6/gcc_64/include -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimediaWidgets -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore -I. -I/usr/include/c++/9 -I/usr/include/x86_64-linux-gnu/c++/9 -I/usr/include/c++/9/backward -I/usr/lib/gcc/x86_64-linux-gnu/9/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include ../modelinference.h -o build/moc/moc_modelinference.cpp

build/moc/moc_camerawidget.cpp: ../camerawidget.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QWidget \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qwidget.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qtwidgetsglobal.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtguiglobal.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qglobal.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qconfig.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtcore-config.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsystemdetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qprocessordetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcompilerdetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtypeinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsysinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlogging.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qflags.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbasicatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qgenericatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_cxx11.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_msvc.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qglobalstatic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmutex.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qnumeric.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qversiontagging.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtgui-config.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qtwidgets-config.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qwindowdefs.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobjectdefs.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qnamespace.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qwindowdefs_win.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobject.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstring.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qchar.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbytearray.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qrefcount.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qarraydata.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringliteral.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringalgorithms.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringview.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringbuilder.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qalgorithms.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qiterator.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qhashfunctions.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qpair.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbytearraylist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringlist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qregexp.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringmatcher.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcoreevent.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qscopedpointer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmetatype.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvarlengtharray.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcontainerfwd.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobject_impl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmargins.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpaintdevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qrect.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsize.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qpoint.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpalette.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qcolor.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qrgb.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qrgba64.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qbrush.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvector.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qmatrix.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpolygon.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qregion.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdatastream.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qiodevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qline.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtransform.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpainterpath.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qimage.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpixelformat.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpixmap.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsharedpointer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qshareddata.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qhash.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qfont.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qfontmetrics.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qfontinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qsizepolicy.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qcursor.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qkeysequence.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qevent.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariant.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmap.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdebug.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtextstream.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlocale.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qset.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcontiguouscache.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qurl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qurlquery.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfile.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfiledevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qvector2d.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtouchdevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QVBoxLayout \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qboxlayout.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlayout.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlayoutitem.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qgridlayout.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QHBoxLayout \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QPushButton \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qpushbutton.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractbutton.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qicon.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QLabel \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlabel.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qframe.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QComboBox \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qcombobox.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractitemdelegate.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractspinbox.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qvalidator.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qregularexpression.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qslider.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractslider.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyle.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qtabbar.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qtabwidget.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qrubberband.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qabstractitemmodel.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QTimer \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtimer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbasictimer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/QCamera \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qcamera.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qmediacontrol.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qtmultimediaglobal.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qtmultimedia-config.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qmediaobject.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qmultimedia.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qmediaservice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qcameraexposure.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qmediaenumdebug.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmetaobject.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qcamerafocus.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qcameraimageprocessing.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qcameraviewfindersettings.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qvideoframe.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qabstractvideobuffer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimediaWidgets/QCameraViewfinder \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimediaWidgets/qcameraviewfinder.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimediaWidgets/qvideowidget.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimediaWidgets/qtmultimediawidgetdefs.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qmediabindableinterface.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/QCameraImageCapture \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qcameraimagecapture.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qmediaencodersettings.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/QCameraInfo \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qcamerainfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/QImageEncoderSettings \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/QPixmap \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QFileDialog \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qfiledialog.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdir.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfileinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qdialog.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QStandardPaths \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstandardpaths.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QDir \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QDebug \
		build/moc/moc_predefs.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/bin/moc
	/opt/Qt5.12.6/5.12.6/gcc_64/bin/moc $(DEFINES) --include /mnt/e/code/diagnosis/build/build/moc/moc_predefs.h -I/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/linux-g++ -I/mnt/e/code/diagnosis -I/mnt/e/code/diagnosis -I/opt/Qt5.12.6/5.12.6/gcc_64/include -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimediaWidgets -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore -I. -I/usr/include/c++/9 -I/usr/include/x86_64-linux-gnu/c++/9 -I/usr/include/c++/9/backward -I/usr/lib/gcc/x86_64-linux-gnu/9/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include ../camerawidget.h -o build/moc/moc_camerawidget.cpp

compiler_moc_objc_header_make_all:
compiler_moc_objc_header_clean:
compiler_moc_source_make_all:
compiler_moc_source_clean:
compiler_uic_make_all:
compiler_uic_clean:
compiler_yacc_decl_make_all:
compiler_yacc_decl_clean:
compiler_yacc_impl_make_all:
compiler_yacc_impl_clean:
compiler_lex_make_all:
compiler_lex_clean:
compiler_clean: compiler_rcc_clean compiler_moc_predefs_clean compiler_moc_header_clean 

####### Compile

build/obj/main.o: ../main.cpp /opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QApplication \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qapplication.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qtwidgetsglobal.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtguiglobal.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qglobal.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qconfig.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtcore-config.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsystemdetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qprocessordetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcompilerdetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtypeinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsysinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlogging.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qflags.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbasicatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qgenericatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_cxx11.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_msvc.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qglobalstatic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmutex.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qnumeric.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qversiontagging.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtgui-config.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qtwidgets-config.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcoreapplication.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstring.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qchar.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbytearray.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qrefcount.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qnamespace.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qarraydata.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringliteral.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringalgorithms.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringview.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringbuilder.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobject.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobjectdefs.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qalgorithms.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qiterator.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qhashfunctions.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qpair.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbytearraylist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringlist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qregexp.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringmatcher.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcoreevent.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qscopedpointer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmetatype.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvarlengtharray.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcontainerfwd.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobject_impl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qeventloop.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qwindowdefs.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qwindowdefs_win.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qpoint.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsize.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qcursor.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qdesktopwidget.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qwidget.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmargins.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpaintdevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qrect.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpalette.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qcolor.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qrgb.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qrgba64.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qbrush.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvector.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qmatrix.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpolygon.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qregion.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdatastream.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qiodevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qline.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtransform.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpainterpath.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qimage.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpixelformat.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpixmap.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsharedpointer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qshareddata.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qhash.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qfont.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qfontmetrics.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qfontinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qsizepolicy.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qkeysequence.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qevent.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariant.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmap.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdebug.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtextstream.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlocale.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qset.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcontiguouscache.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qurl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qurlquery.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfile.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfiledevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qvector2d.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtouchdevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qguiapplication.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qinputmethod.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QStyleFactory \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstylefactory.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QDir \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdir.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfileinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QStandardPaths \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstandardpaths.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QDebug \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/QFont \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/QFontDatabase \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qfontdatabase.h \
		../mainwindow.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QMainWindow \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qmainwindow.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qtabwidget.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qicon.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QStackedWidget \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstackedwidget.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qframe.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QVBoxLayout \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qboxlayout.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlayout.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlayoutitem.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qgridlayout.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QHBoxLayout \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QPushButton \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qpushbutton.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractbutton.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QLabel \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlabel.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QProgressBar \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qprogressbar.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QStatusBar \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstatusbar.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QMenuBar \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qmenubar.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qmenu.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qaction.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qactiongroup.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QAction \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QTimer \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtimer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbasictimer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/QKeyEvent \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/QCloseEvent \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QMessageBox \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qmessagebox.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qdialog.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/obj/main.o ../main.cpp

build/obj/mainwindow.o: ../mainwindow.cpp ../mainwindow.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QMainWindow \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qmainwindow.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qtwidgetsglobal.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtguiglobal.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qglobal.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qconfig.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtcore-config.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsystemdetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qprocessordetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcompilerdetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtypeinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsysinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlogging.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qflags.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbasicatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qgenericatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_cxx11.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_msvc.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qglobalstatic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmutex.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qnumeric.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qversiontagging.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtgui-config.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qtwidgets-config.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qwidget.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qwindowdefs.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobjectdefs.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qnamespace.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qwindowdefs_win.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobject.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstring.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qchar.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbytearray.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qrefcount.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qarraydata.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringliteral.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringalgorithms.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringview.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringbuilder.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qalgorithms.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qiterator.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qhashfunctions.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qpair.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbytearraylist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringlist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qregexp.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringmatcher.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcoreevent.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qscopedpointer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmetatype.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvarlengtharray.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcontainerfwd.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobject_impl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmargins.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpaintdevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qrect.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsize.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qpoint.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpalette.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qcolor.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qrgb.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qrgba64.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qbrush.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvector.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qmatrix.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpolygon.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qregion.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdatastream.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qiodevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qline.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtransform.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpainterpath.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qimage.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpixelformat.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpixmap.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsharedpointer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qshareddata.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qhash.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qfont.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qfontmetrics.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qfontinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qsizepolicy.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qcursor.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qkeysequence.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qevent.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariant.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmap.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdebug.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtextstream.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlocale.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qset.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcontiguouscache.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qurl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qurlquery.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfile.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfiledevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qvector2d.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtouchdevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qtabwidget.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qicon.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QStackedWidget \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstackedwidget.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qframe.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QVBoxLayout \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qboxlayout.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlayout.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlayoutitem.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qgridlayout.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QHBoxLayout \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QPushButton \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qpushbutton.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractbutton.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QLabel \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlabel.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QProgressBar \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qprogressbar.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QStatusBar \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstatusbar.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QMenuBar \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qmenubar.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qmenu.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qaction.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qactiongroup.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QAction \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QTimer \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtimer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbasictimer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/QKeyEvent \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/QCloseEvent \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QMessageBox \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qmessagebox.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qdialog.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QDebug \
		../taskpage.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QWidget \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QGridLayout \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QTextEdit \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qtextedit.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractscrollarea.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtextdocument.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtextoption.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtextcursor.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtextformat.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpen.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QLineEdit \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlineedit.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QFrame \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QScrollArea \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qscrollarea.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QFileDialog \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qfiledialog.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdir.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfileinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/QPixmap \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/QMovie \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qmovie.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qimagereader.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcoreapplication.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qeventloop.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qimageiohandler.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qplugin.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qpointer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qjsonobject.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qjsonvalue.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfactoryinterface.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/QDragEnterEvent \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/QDropEvent \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QMimeData \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmimedata.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QUrl \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QJsonDocument \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qjsondocument.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QJsonArray \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qjsonarray.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QJsonObject \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QFile \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QRadioButton \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qradiobutton.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QButtonGroup \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qbuttongroup.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QGroupBox \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qgroupbox.h \
		../taskdata.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QString \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QStringList \
		../camerawidget.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QComboBox \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qcombobox.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractitemdelegate.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractspinbox.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qvalidator.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qregularexpression.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qslider.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractslider.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyle.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qtabbar.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qrubberband.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qabstractitemmodel.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/QCamera \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qcamera.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qmediacontrol.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qtmultimediaglobal.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qtmultimedia-config.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qmediaobject.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qmultimedia.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qmediaservice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qcameraexposure.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qmediaenumdebug.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmetaobject.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qcamerafocus.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qcameraimageprocessing.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qcameraviewfindersettings.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qvideoframe.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qabstractvideobuffer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimediaWidgets/QCameraViewfinder \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimediaWidgets/qcameraviewfinder.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimediaWidgets/qvideowidget.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimediaWidgets/qtmultimediawidgetdefs.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qmediabindableinterface.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/QCameraImageCapture \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qcameraimagecapture.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qmediaencodersettings.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/QCameraInfo \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qcamerainfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/QImageEncoderSettings \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QStandardPaths \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstandardpaths.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QDir \
		../modelinference.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QObject \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QThread \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qthread.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QProcess \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qprocess.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QFileInfo \
		../taskmanager.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QApplication \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qapplication.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qdesktopwidget.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qguiapplication.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qinputmethod.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QDateTime \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdatetime.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QSettings \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsettings.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/QScreen \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qscreen.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QList \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QRect \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QSize \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QSizeF \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/QTransform
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/obj/mainwindow.o ../mainwindow.cpp

build/obj/taskpage.o: ../taskpage.cpp ../taskpage.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QWidget \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qwidget.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qtwidgetsglobal.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtguiglobal.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qglobal.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qconfig.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtcore-config.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsystemdetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qprocessordetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcompilerdetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtypeinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsysinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlogging.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qflags.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbasicatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qgenericatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_cxx11.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_msvc.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qglobalstatic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmutex.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qnumeric.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qversiontagging.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtgui-config.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qtwidgets-config.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qwindowdefs.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobjectdefs.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qnamespace.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qwindowdefs_win.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobject.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstring.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qchar.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbytearray.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qrefcount.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qarraydata.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringliteral.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringalgorithms.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringview.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringbuilder.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qalgorithms.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qiterator.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qhashfunctions.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qpair.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbytearraylist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringlist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qregexp.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringmatcher.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcoreevent.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qscopedpointer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmetatype.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvarlengtharray.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcontainerfwd.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobject_impl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmargins.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpaintdevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qrect.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsize.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qpoint.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpalette.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qcolor.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qrgb.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qrgba64.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qbrush.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvector.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qmatrix.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpolygon.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qregion.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdatastream.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qiodevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qline.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtransform.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpainterpath.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qimage.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpixelformat.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpixmap.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsharedpointer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qshareddata.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qhash.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qfont.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qfontmetrics.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qfontinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qsizepolicy.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qcursor.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qkeysequence.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qevent.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariant.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmap.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdebug.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtextstream.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlocale.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qset.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcontiguouscache.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qurl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qurlquery.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfile.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfiledevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qvector2d.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtouchdevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QVBoxLayout \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qboxlayout.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlayout.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlayoutitem.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qgridlayout.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QHBoxLayout \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QGridLayout \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QLabel \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlabel.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qframe.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QPushButton \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qpushbutton.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractbutton.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qicon.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QTextEdit \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qtextedit.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractscrollarea.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtextdocument.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtextoption.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtextcursor.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtextformat.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpen.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QLineEdit \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlineedit.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QFrame \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QScrollArea \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qscrollarea.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QProgressBar \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qprogressbar.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QTimer \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtimer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbasictimer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QFileDialog \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qfiledialog.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdir.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfileinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qdialog.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/QPixmap \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/QMovie \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qmovie.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qimagereader.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcoreapplication.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qeventloop.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qimageiohandler.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qplugin.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qpointer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qjsonobject.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qjsonvalue.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfactoryinterface.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/QDragEnterEvent \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/QDropEvent \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QMimeData \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmimedata.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QUrl \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QDebug \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QJsonDocument \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qjsondocument.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QJsonArray \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qjsonarray.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QJsonObject \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QFile \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QRadioButton \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qradiobutton.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QButtonGroup \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qbuttongroup.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QGroupBox \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qgroupbox.h \
		../taskdata.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QString \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QStringList \
		../camerawidget.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QComboBox \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qcombobox.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractitemdelegate.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractspinbox.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qvalidator.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qregularexpression.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qslider.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractslider.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyle.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qtabbar.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qtabwidget.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qrubberband.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qabstractitemmodel.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/QCamera \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qcamera.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qmediacontrol.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qtmultimediaglobal.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qtmultimedia-config.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qmediaobject.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qmultimedia.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qmediaservice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qcameraexposure.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qmediaenumdebug.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmetaobject.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qcamerafocus.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qcameraimageprocessing.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qcameraviewfindersettings.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qvideoframe.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qabstractvideobuffer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimediaWidgets/QCameraViewfinder \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimediaWidgets/qcameraviewfinder.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimediaWidgets/qvideowidget.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimediaWidgets/qtmultimediawidgetdefs.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qmediabindableinterface.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/QCameraImageCapture \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qcameraimagecapture.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qmediaencodersettings.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/QCameraInfo \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qcamerainfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/QImageEncoderSettings \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QStandardPaths \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstandardpaths.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QDir \
		../modelinference.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QObject \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QThread \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qthread.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QProcess \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qprocess.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QFileInfo \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QApplication \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qapplication.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qdesktopwidget.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qguiapplication.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qinputmethod.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/QImageReader \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QDateTime \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdatetime.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QMessageBox \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qmessagebox.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/obj/taskpage.o ../taskpage.cpp

build/obj/taskmanager.o: ../taskmanager.cpp ../taskmanager.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QObject \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobject.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobjectdefs.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qnamespace.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qglobal.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qconfig.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtcore-config.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsystemdetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qprocessordetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcompilerdetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtypeinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsysinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlogging.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qflags.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbasicatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qgenericatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_cxx11.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_msvc.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qglobalstatic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmutex.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qnumeric.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qversiontagging.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstring.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qchar.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbytearray.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qrefcount.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qarraydata.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringliteral.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringalgorithms.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringview.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringbuilder.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qalgorithms.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qiterator.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qhashfunctions.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qpair.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbytearraylist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringlist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qregexp.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringmatcher.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcoreevent.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qscopedpointer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmetatype.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvarlengtharray.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcontainerfwd.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobject_impl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QJsonDocument \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qjsondocument.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qjsonvalue.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QJsonObject \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qjsonobject.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QJsonArray \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qjsonarray.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QFile \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfile.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfiledevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qiodevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QDir \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdir.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfileinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qshareddata.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qhash.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QStandardPaths \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstandardpaths.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QDebug \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdebug.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmap.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtextstream.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlocale.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariant.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvector.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qpoint.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qset.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcontiguouscache.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsharedpointer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsharedpointer_impl.h \
		../taskdata.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QString \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QStringList \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QDateTime \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdatetime.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/obj/taskmanager.o ../taskmanager.cpp

build/obj/modelinference.o: ../modelinference.cpp ../modelinference.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QObject \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobject.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobjectdefs.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qnamespace.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qglobal.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qconfig.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtcore-config.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsystemdetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qprocessordetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcompilerdetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtypeinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsysinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlogging.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qflags.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbasicatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qgenericatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_cxx11.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_msvc.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qglobalstatic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmutex.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qnumeric.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qversiontagging.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstring.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qchar.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbytearray.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qrefcount.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qarraydata.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringliteral.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringalgorithms.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringview.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringbuilder.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qalgorithms.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qiterator.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qhashfunctions.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qpair.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbytearraylist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringlist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qregexp.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringmatcher.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcoreevent.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qscopedpointer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmetatype.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvarlengtharray.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcontainerfwd.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobject_impl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QThread \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qthread.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QProcess \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qprocess.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qiodevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qshareddata.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qhash.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QTimer \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtimer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbasictimer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QJsonDocument \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qjsondocument.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qjsonvalue.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QJsonObject \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qjsonobject.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QJsonArray \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qjsonarray.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QFileInfo \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfileinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfile.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfiledevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QDir \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdir.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QDebug \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdebug.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmap.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtextstream.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlocale.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariant.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvector.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qpoint.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qset.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcontiguouscache.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsharedpointer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsharedpointer_impl.h \
		../taskdata.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QString \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QStringList \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QCoreApplication \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcoreapplication.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qeventloop.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QStandardPaths \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstandardpaths.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QJsonParseError \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QDateTime \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdatetime.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/obj/modelinference.o ../modelinference.cpp

build/obj/camerawidget.o: ../camerawidget.cpp ../camerawidget.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QWidget \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qwidget.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qtwidgetsglobal.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtguiglobal.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qglobal.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qconfig.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtcore-config.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsystemdetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qprocessordetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcompilerdetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtypeinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsysinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlogging.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qflags.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbasicatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qgenericatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_cxx11.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_msvc.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qglobalstatic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmutex.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qnumeric.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qversiontagging.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtgui-config.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qtwidgets-config.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qwindowdefs.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobjectdefs.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qnamespace.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qwindowdefs_win.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobject.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstring.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qchar.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbytearray.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qrefcount.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qarraydata.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringliteral.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringalgorithms.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringview.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringbuilder.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qalgorithms.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qiterator.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qhashfunctions.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qpair.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbytearraylist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringlist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qregexp.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringmatcher.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcoreevent.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qscopedpointer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmetatype.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvarlengtharray.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcontainerfwd.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobject_impl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmargins.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpaintdevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qrect.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsize.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qpoint.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpalette.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qcolor.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qrgb.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qrgba64.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qbrush.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvector.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qmatrix.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpolygon.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qregion.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdatastream.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qiodevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qline.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtransform.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpainterpath.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qimage.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpixelformat.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpixmap.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsharedpointer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qshareddata.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qhash.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qfont.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qfontmetrics.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qfontinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qsizepolicy.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qcursor.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qkeysequence.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qevent.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariant.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmap.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdebug.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtextstream.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlocale.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qset.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcontiguouscache.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qurl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qurlquery.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfile.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfiledevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qvector2d.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtouchdevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QVBoxLayout \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qboxlayout.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlayout.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlayoutitem.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qgridlayout.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QHBoxLayout \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QPushButton \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qpushbutton.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractbutton.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qicon.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QLabel \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlabel.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qframe.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QComboBox \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qcombobox.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractitemdelegate.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractspinbox.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qvalidator.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qregularexpression.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qslider.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractslider.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyle.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qtabbar.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qtabwidget.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qrubberband.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qabstractitemmodel.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QTimer \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtimer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbasictimer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/QCamera \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qcamera.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qmediacontrol.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qtmultimediaglobal.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qtmultimedia-config.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qmediaobject.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qmultimedia.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qmediaservice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qcameraexposure.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qmediaenumdebug.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmetaobject.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qcamerafocus.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qcameraimageprocessing.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qcameraviewfindersettings.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qvideoframe.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qabstractvideobuffer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimediaWidgets/QCameraViewfinder \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimediaWidgets/qcameraviewfinder.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimediaWidgets/qvideowidget.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimediaWidgets/qtmultimediawidgetdefs.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qmediabindableinterface.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/QCameraImageCapture \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qcameraimagecapture.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qmediaencodersettings.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/QCameraInfo \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/qcamerainfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia/QImageEncoderSettings \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/QPixmap \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QFileDialog \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qfiledialog.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdir.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfileinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qdialog.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QStandardPaths \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstandardpaths.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QDir \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QDebug \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QDateTime \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdatetime.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/obj/camerawidget.o ../camerawidget.cpp

build/obj/qrc_DiagnosisApp.o: build/rcc/qrc_DiagnosisApp.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/obj/qrc_DiagnosisApp.o build/rcc/qrc_DiagnosisApp.cpp

build/obj/moc_mainwindow.o: build/moc/moc_mainwindow.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/obj/moc_mainwindow.o build/moc/moc_mainwindow.cpp

build/obj/moc_taskpage.o: build/moc/moc_taskpage.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/obj/moc_taskpage.o build/moc/moc_taskpage.cpp

build/obj/moc_taskmanager.o: build/moc/moc_taskmanager.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/obj/moc_taskmanager.o build/moc/moc_taskmanager.cpp

build/obj/moc_modelinference.o: build/moc/moc_modelinference.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/obj/moc_modelinference.o build/moc/moc_modelinference.cpp

build/obj/moc_camerawidget.o: build/moc/moc_camerawidget.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/obj/moc_camerawidget.o build/moc/moc_camerawidget.cpp

####### Install

install_target: first FORCE
	@test -d $(INSTALL_ROOT)/usr/local/bin || mkdir -p $(INSTALL_ROOT)/usr/local/bin
	-$(QINSTALL_PROGRAM) $(QMAKE_TARGET) $(INSTALL_ROOT)/usr/local/bin/$(QMAKE_TARGET)
	-$(STRIP) $(INSTALL_ROOT)/usr/local/bin/$(QMAKE_TARGET)

uninstall_target: FORCE
	-$(DEL_FILE) $(INSTALL_ROOT)/usr/local/bin/$(QMAKE_TARGET)
	-$(DEL_DIR) $(INSTALL_ROOT)/usr/local/bin/ 


install: install_target  FORCE

uninstall: uninstall_target  FORCE

FORCE:

