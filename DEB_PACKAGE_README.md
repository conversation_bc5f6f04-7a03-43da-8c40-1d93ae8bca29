# 模型推理测试系统 .deb 包使用说明

## 概述

本文档介绍如何构建、安装和使用模型推理测试系统的 .deb 包。该应用程序专为 arm64 Debian 系统设计，支持通用图像推理测试功能。

## 系统要求

- **操作系统**: arm64 Debian (推荐 Debian 11 或更高版本)
- **架构**: ARM64 (aarch64)
- **Qt版本**: Qt 5.15.2 或兼容版本
- **依赖包**: qtbase5-dev, qtmultimedia5-dev, libqt5multimedia5-plugins

## 快速开始

### 1. 一键构建和打包

```bash
# 给脚本执行权限
chmod +x build_and_package.sh

# 执行一键构建和打包
./build_and_package.sh
```

### 2. 手动构建步骤

如果需要分步执行，可以按以下步骤操作：

```bash
# 步骤 1: 编译项目
chmod +x build.sh
./build.sh

# 步骤 2: 创建 .deb 包
chmod +x create_deb_package.sh
./create_deb_package.sh
```

## 安装和卸载

### 安装应用程序

```bash
# 安装 .deb 包
sudo dpkg -i inference-test-app_1.0.0_arm64.deb

# 如果有依赖问题，运行以下命令修复
sudo apt-get install -f
```

### 卸载应用程序

```bash
# 卸载但保留配置文件
sudo apt remove diagnosis-app

# 完全卸载（包括配置文件）
sudo apt purge diagnosis-app
```

## 运行应用程序

安装完成后，可以通过以下方式运行应用程序：

### 方式 1: 命令行快捷方式
```bash
diagnosis-app
```

### 方式 2: 直接运行
```bash
/opt/inference-test-app/run_inference_app.sh
```

### 方式 3: 桌面应用（如果有桌面环境）
在应用程序菜单中找到"诊断推理应用"

## 配置文件

### 主配置文件位置
- **系统配置**: `/etc/diagnosis-app/inference.conf`
- **应用配置**: `/opt/diagnosis-app/config/`

### 配置文件说明

主配置文件 `/etc/diagnosis-app/inference.conf` 包含以下可配置参数：

#### [General] - 通用设置
- `timeout_seconds`: 推理超时时间（秒），默认 300
- `temp_directory`: 临时文件目录，默认 `/tmp/diagnosis-app`
- `log_level`: 日志级别 (DEBUG, INFO, WARNING, ERROR)
- `log_file`: 日志文件路径

#### [RK3588_Inference] - RK3588推理设置
- `demo_path`: RK3588推理可执行文件路径
- `rknn_model_path`: RKNN模型文件路径
- `rkllm_model_path`: RKLLM模型文件路径
- `max_tokens`: 最大令牌数，默认 128
- `context_length`: 上下文长度，默认 512
- `core_num`: CPU核心数，默认 4
- `auto_chinese_output`: 自动选择中文输出，默认 true

#### [Standard_Inference] - 标准推理设置
- `executable_path`: 标准推理可执行文件路径
- `enable_mock_result`: 启用模拟结果，默认 true
- `mock_result_delay`: 模拟结果延迟（毫秒），默认 2000

#### [Camera_Settings] - 相机设置
- `camera_detection_mode`: 相机检测模式 (auto/manual)
- `manual_camera_devices`: 手动指定相机设备路径
- `camera_init_timeout`: 相机初始化超时时间
- `image_save_quality`: 图片保存质量 (1-100)

### 配置文件示例

```ini
[General]
timeout_seconds=300
temp_directory=/tmp/diagnosis-app
log_level=INFO

[RK3588_Inference]
demo_path=/userdata/install/demo_Linux_aarch64/demo
rknn_model_path=/userdata/models/qwen2_vl_2b_vision_rk3588.rknn
rkllm_model_path=/userdata/models/Qwen2-2B-vl-Instruct.rkllm
max_tokens=128
context_length=512
auto_chinese_output=true

[Camera_Settings]
camera_detection_mode=auto
image_save_quality=85
```

## 文件结构

安装后的文件结构：

```
/opt/inference-test-app/
├── InferenceApp                 # 主可执行文件
├── run_inference_app.sh         # 启动脚本
├── config/                      # 配置文件目录
│   ├── inference.conf.example   # 配置文件示例
│   ├── default_tasks.json       # 默认任务配置
│   └── questions.json           # 问题配置
├── models/                      # 模型文件目录（如果存在）
├── images/                      # 图片资源目录（如果存在）
└── icons/                       # 图标文件目录

/etc/diagnosis-app/
└── inference.conf               # 主配置文件

/var/log/diagnosis-app/
└── app.log                      # 应用日志文件
```

## 日志和调试

### 查看日志
```bash
# 查看应用日志
tail -f /var/log/diagnosis-app/app.log

# 查看系统日志
journalctl -u diagnosis-app
```

### 调试模式
如果需要调试，可以直接运行可执行文件：
```bash
cd /opt/diagnosis-app
./DiagnosisApp
```

## 常见问题

### 1. 依赖包缺失
如果安装时提示依赖包缺失，请运行：
```bash
sudo apt update
sudo apt install qtbase5-dev qtmultimedia5-dev libqt5multimedia5-plugins
```

### 2. 权限问题
如果运行时出现权限问题：
```bash
sudo chown -R $USER:$USER /var/log/diagnosis-app
```

### 3. 配置文件不生效
确保配置文件格式正确，并重启应用程序：
```bash
# 检查配置文件语法
cat /etc/diagnosis-app/inference.conf

# 重启应用程序
pkill DiagnosisApp
diagnosis-app
```

### 4. 模型文件路径问题
确保配置文件中的模型路径正确，并且文件存在：
```bash
# 检查模型文件是否存在
ls -la /userdata/models/
```

## 开发和定制

### 修改配置
1. 编辑配置文件：`sudo nano /etc/diagnosis-app/inference.conf`
2. 重启应用程序使配置生效

### 添加新模型
1. 将模型文件放置到指定目录
2. 更新配置文件中的模型路径
3. 重启应用程序

### 自定义启动脚本
可以修改 `/opt/inference-test-app/run_inference_app.sh` 来自定义启动参数和环境变量。

## 技术支持

如果遇到问题，请检查：
1. 系统架构是否为 arm64
2. Qt 依赖包是否正确安装
3. 配置文件格式是否正确
4. 模型文件路径是否存在
5. 日志文件中的错误信息

## 版本信息

- **当前版本**: 1.0.0
- **支持架构**: arm64
- **Qt版本**: 5.15.2+
- **系统要求**: Debian 11+
