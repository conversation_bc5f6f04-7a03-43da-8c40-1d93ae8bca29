#!/bin/bash
# 模型推理测试系统环境一键安装脚本
# 适用于arm64 Debian系统

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志文件
INSTALL_LOG="environment_setup.log"
TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')

# 初始化日志
init_log() {
    cat > "$INSTALL_LOG" << EOF
================================================================================
模型推理测试系统环境安装日志
================================================================================
安装时间: $TIMESTAMP
系统架构: $(uname -m)
操作系统: $(lsb_release -d 2>/dev/null | cut -f2 || echo "Unknown")
================================================================================

EOF
}

# 记录日志
log_info() {
    local message="$1"
    echo -e "${BLUE}[INFO]${NC} $message"
    echo "[INFO] $message" >> "$INSTALL_LOG"
}

log_success() {
    local message="$1"
    echo -e "${GREEN}[SUCCESS]${NC} $message"
    echo "[SUCCESS] $message" >> "$INSTALL_LOG"
}

log_warning() {
    local message="$1"
    echo -e "${YELLOW}[WARNING]${NC} $message"
    echo "[WARNING] $message" >> "$INSTALL_LOG"
}

log_error() {
    local message="$1"
    echo -e "${RED}[ERROR]${NC} $message"
    echo "[ERROR] $message" >> "$INSTALL_LOG"
}

# 检查是否为root用户
check_root() {
    if [ "$EUID" -ne 0 ]; then
        log_error "此脚本需要root权限运行"
        echo "请使用: sudo $0"
        exit 1
    fi
}

# 检查系统架构
check_architecture() {
    log_info "检查系统架构..."
    local arch=$(uname -m)
    if [ "$arch" != "aarch64" ]; then
        log_error "不支持的系统架构: $arch"
        log_error "此脚本仅支持arm64(aarch64)架构"
        exit 1
    fi
    log_success "系统架构检查通过: $arch"
}

# 更新软件包列表
update_package_list() {
    log_info "更新软件包列表..."
    if apt update >> "$INSTALL_LOG" 2>&1; then
        log_success "软件包列表更新完成"
    else
        log_error "软件包列表更新失败"
        exit 1
    fi
}

# 安装基础编译工具
install_build_tools() {
    log_info "安装基础编译工具..."
    
    local packages=(
        "build-essential"
        "pkg-config"
        "cmake"
        "git"
    )
    
    for package in "${packages[@]}"; do
        if ! dpkg -l | grep -q "^ii  $package "; then
            log_info "安装 $package..."
            if apt install -y "$package" >> "$INSTALL_LOG" 2>&1; then
                log_success "$package 安装完成"
            else
                log_error "$package 安装失败"
                return 1
            fi
        else
            log_info "$package 已安装"
        fi
    done
    
    log_success "基础编译工具安装完成"
}

# 安装Qt5开发环境
install_qt5() {
    log_info "安装Qt5开发环境..."
    
    local qt_packages=(
        "qtbase5-dev"
        "qtmultimedia5-dev"
        "qttools5-dev"
        "qttools5-dev-tools"
        "libqt5multimedia5-plugins"
        "libqt5multimediawidgets5"
    )
    
    for package in "${qt_packages[@]}"; do
        if ! dpkg -l | grep -q "^ii  $package "; then
            log_info "安装 $package..."
            if apt install -y "$package" >> "$INSTALL_LOG" 2>&1; then
                log_success "$package 安装完成"
            else
                log_error "$package 安装失败"
                return 1
            fi
        else
            log_info "$package 已安装"
        fi
    done
    
    # 验证qmake安装
    if command -v qmake >/dev/null 2>&1; then
        local qmake_version=$(qmake -version | head -1)
        log_success "Qt5开发环境安装完成: $qmake_version"
    else
        log_error "qmake未找到，Qt5安装可能有问题"
        return 1
    fi
}

# 安装多媒体工具
install_multimedia() {
    log_info "安装多媒体工具..."
    
    local multimedia_packages=(
        "ffmpeg"
        "v4l-utils"
        "libv4l-dev"
        "libasound2-dev"
        "libpulse-dev"
    )
    
    for package in "${multimedia_packages[@]}"; do
        if ! dpkg -l | grep -q "^ii  $package "; then
            log_info "安装 $package..."
            if apt install -y "$package" >> "$INSTALL_LOG" 2>&1; then
                log_success "$package 安装完成"
            else
                log_warning "$package 安装失败，但不是关键组件"
            fi
        else
            log_info "$package 已安装"
        fi
    done
    
    log_success "多媒体工具安装完成"
}

# 创建必要目录
create_directories() {
    log_info "创建必要目录..."
    
    local directories=(
        "/userdata"
        "/userdata/install"
        "/userdata/install/demo_Linux_aarch64"
        "/opt/inference-test-app"
        "/etc/inference-test-app"
    )
    
    for dir in "${directories[@]}"; do
        if [ ! -d "$dir" ]; then
            log_info "创建目录: $dir"
            if mkdir -p "$dir"; then
                log_success "目录创建成功: $dir"
            else
                log_error "目录创建失败: $dir"
                return 1
            fi
        else
            log_info "目录已存在: $dir"
        fi
    done
    
    # 设置目录权限
    chmod 755 /userdata
    chmod 755 /userdata/install
    chmod 755 /opt/inference-test-app
    chmod 755 /etc/inference-test-app
    
    log_success "目录结构创建完成"
}

# 配置用户权限
setup_permissions() {
    log_info "配置用户权限..."
    
    # 添加用户到video组（用于摄像头访问）
    if getent group video >/dev/null; then
        local current_user="${SUDO_USER:-$USER}"
        if [ -n "$current_user" ] && [ "$current_user" != "root" ]; then
            usermod -a -G video "$current_user"
            log_success "用户 $current_user 已添加到video组"
        fi
    fi
    
    # 设置/userdata目录权限，允许普通用户访问
    chown -R root:users /userdata 2>/dev/null || true
    chmod -R 775 /userdata 2>/dev/null || true
    
    log_success "权限配置完成"
}

# 检查摄像头设备
check_camera_devices() {
    log_info "检查摄像头设备..."
    
    local video_devices=$(ls /dev/video* 2>/dev/null | wc -l)
    if [ "$video_devices" -gt 0 ]; then
        log_success "发现 $video_devices 个摄像头设备"
        ls /dev/video* 2>/dev/null | while read device; do
            log_info "摄像头设备: $device"
        done
    else
        log_warning "未发现摄像头设备，请检查硬件连接"
    fi
}

# 创建示例配置文件
create_sample_config() {
    log_info "创建示例配置文件..."
    
    local config_file="/etc/inference-test-app/inference.conf"
    if [ ! -f "$config_file" ]; then
        cat > "$config_file" << 'EOF'
[RK3588_Inference]
# RK3588推理程序路径
demo_path=/userdata/install/demo_Linux_aarch64/demo

# RKNN模型文件路径
rknn_model=qwen2_vl_2b_vision_rk3588.rknn

# RKLLM模型文件路径  
rkllm_model=Qwen2-2B-vl-Instruct.rkllm

# 最大token数
max_tokens=128

# 上下文长度
context_length=512

# CPU核心数
core_num=4

[UI_Settings]
# 默认输入提示
default_input_placeholder=请输入相关信息...

[Standard_Inference]
# 标准推理可执行文件路径
executable_path=./inference_engine
EOF
        log_success "示例配置文件创建完成: $config_file"
    else
        log_info "配置文件已存在: $config_file"
    fi
}

# 运行环境检查
run_environment_check() {
    log_info "运行环境检查..."
    
    if [ -f "./check_environment.sh" ]; then
        chmod +x ./check_environment.sh
        ./check_environment.sh
        log_success "环境检查完成，请查看生成的报告"
    else
        log_warning "环境检查脚本不存在，跳过检查"
    fi
}

# 显示安装完成信息
show_completion_info() {
    echo ""
    echo -e "${GREEN}=================================================================================${NC}"
    echo -e "${GREEN}环境安装完成！${NC}"
    echo -e "${GREEN}=================================================================================${NC}"
    echo ""
    echo -e "${BLUE}接下来的步骤：${NC}"
    echo "1. 部署推理模型到 /userdata/install/demo_Linux_aarch64/"
    echo "2. 安装模型推理测试应用的.deb包"
    echo "3. 根据实际情况修改配置文件 /etc/inference-test-app/inference.conf"
    echo ""
    echo -e "${BLUE}安装日志：${NC} $INSTALL_LOG"
    echo ""
    echo -e "${YELLOW}注意：${NC}"
    echo "- 如果添加了用户到video组，需要重新登录才能生效"
    echo "- 确保推理模型文件具有正确的执行权限"
    echo "- 如果遇到权限问题，可能需要调整SELinux或AppArmor设置"
    echo ""
}

# 主函数
main() {
    echo -e "${BLUE}模型推理测试系统环境安装工具${NC}"
    echo "========================================"
    echo ""
    
    init_log
    check_root
    check_architecture
    update_package_list
    install_build_tools
    install_qt5
    install_multimedia
    create_directories
    setup_permissions
    check_camera_devices
    create_sample_config
    run_environment_check
    show_completion_info
}

# 执行主函数
main "$@"
