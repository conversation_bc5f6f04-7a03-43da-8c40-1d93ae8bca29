#include "taskpage.h"
#include "modelmanager.h"
#include <QApplication>
#include <QMimeData>
#include <QImageReader>
#include <QStandardPaths>
#include <QDateTime>
#include <QMessageBox>
#include <QFileDialog>
#include <QProcess>
#include <QDir>
#include <QThread>
#include <QMetaObject>
#include <algorithm>

// 常量定义已在头文件中声明，这里不需要重复定义

TaskPage::TaskPage(QWidget *parent)
    : QWidget(parent)
    , m_currentQuestionIndex(0)
    , m_mainLayout(nullptr)
    // , m_cameraWidget(nullptr) // 移除CameraWidget UI组件
    , m_modelInference(nullptr)
    , m_hasImage(false)
    , m_isInferencing(false)
    , m_progressTimer(new QTimer(this))
    , m_choicesButtonGroup(new QButtonGroup(this))
    , m_progressValue(0)
    , m_availableCameraDevice("")
    , m_modelManager(nullptr)
{
    setupUI();
    connectSignals();

    // 设置拖拽支持
    setAcceptDrops(true);

    // 检测可用的摄像头设备
    detectAvailableCamera();

    qDebug() << "TaskPage created";
}

TaskPage::~TaskPage()
{
    qDebug() << "[调试] TaskPage析构开始";

    // 停止推理进程
    m_isInferencing = false;

    // 停止并清理定时器
    if (m_progressTimer) {
        m_progressTimer->stop();
        m_progressTimer->deleteLater();
        m_progressTimer = nullptr;
    }

    // 清理所有子进程（查找所有QProcess子对象）
    QList<QProcess*> processes = findChildren<QProcess*>();
    for (QProcess* process : processes) {
        if (process && process->state() != QProcess::NotRunning) {
            qDebug() << "[调试] 终止进程:" << process;
            process->kill();
            if (!process->waitForFinished(3000)) {
                qDebug() << "[调试] 强制终止进程";
                process->terminate();
            }
        }
    }

    // 清理所有定时器
    QList<QTimer*> timers = findChildren<QTimer*>();
    for (QTimer* timer : timers) {
        if (timer) {
            timer->stop();
            timer->deleteLater();
        }
    }

    qDebug() << "[调试] TaskPage析构完成";
}

void TaskPage::setupUI()
{
    // 主布局 - 水平布局
    m_mainLayout = new QHBoxLayout(this);
    m_mainLayout->setContentsMargins(10, 10, 10, 10);
    m_mainLayout->setSpacing(15);

    // 设置各个区域
    setupOperationSection();
    setupQuestionSection();
    setupImageSection();
    setupInferenceSection();

    // 初始化组件
    // m_cameraWidget = new CameraWidget(this); // 移除CameraWidget UI组件
    m_modelInference = new ModelInference(this);

    // 设置样式
    setStyleSheet(R"(
        QFrame {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
        }
        QPushButton {
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #0056b3;
        }
        QPushButton:pressed {
            background-color: #004085;
        }
        QPushButton:disabled {
            background-color: #6c757d;
        }
        QLabel#titleLabel {
            font-size: 16px;
            font-weight: bold;
            color: #212529;
        }
        QTextEdit {
            border: 1px solid #ced4da;
            border-radius: 4px;
            padding: 8px;
        }
    )");
}

void TaskPage::setupOperationSection()
{
    // 左侧操作指引区域 - 占据1/3空间
    m_operationFrame = new QFrame();
    m_operationFrame->setMinimumWidth(300);
    m_operationFrame->setMaximumWidth(350);
    m_operationLayout = new QVBoxLayout(m_operationFrame);
    m_operationLayout->setContentsMargins(20, 20, 20, 20);
    m_operationLayout->setSpacing(15);

    // 标题
    m_operationTitle = new QLabel("操作事项");
    m_operationTitle->setObjectName("titleLabel");
    m_operationTitle->setAlignment(Qt::AlignCenter);
    m_operationTitle->setStyleSheet("font-size: 20px; font-weight: bold; color: #0078d4; margin-bottom: 10px;");
    m_operationLayout->addWidget(m_operationTitle);

    // 操作步骤 - 增大字体和间距
    m_step1Label = new QLabel("步骤1: 根据测试要求准备待推理的图像内容");
    m_step1Label->setWordWrap(true);
    m_step1Label->setStyleSheet("color: #000000; font-size: 16px; padding: 8px; background-color: #f8f9fa; border-radius: 6px; margin: 4px;");
    m_operationLayout->addWidget(m_step1Label);

    m_step2Label = new QLabel("步骤2: 确认图像清晰后点击拍照按钮或上传图片");
    m_step2Label->setWordWrap(true);
    m_step2Label->setStyleSheet("color: #000000; font-size: 16px; padding: 8px; background-color: #f8f9fa; border-radius: 6px; margin: 4px;");
    m_operationLayout->addWidget(m_step2Label);

    m_step3Label = new QLabel("步骤3: 确认图片无误后点击开始推理进行模型调用");
    m_step3Label->setWordWrap(true);
    m_step3Label->setStyleSheet("color: #000000; font-size: 16px; padding: 8px; background-color: #f8f9fa; border-radius: 6px; margin: 4px;");
    m_operationLayout->addWidget(m_step3Label);

    m_step4Label = new QLabel("步骤4: 确认模型输出后对界面进行截屏，请确保截图中含题目、图像及模型输出内容");
    m_step4Label->setWordWrap(true);
    m_step4Label->setStyleSheet("color: #000000; font-size: 16px; padding: 8px; background-color: #f8f9fa; border-radius: 6px; margin: 4px;");
    m_operationLayout->addWidget(m_step4Label);

    m_step5Label = new QLabel("步骤5: 确认每道题目都回答完毕后退出应用");
    m_step5Label->setWordWrap(true);
    m_step5Label->setStyleSheet("color: #000000; font-size: 16px; padding: 8px; background-color: #f8f9fa; border-radius: 6px; margin: 4px;");
    m_operationLayout->addWidget(m_step5Label);

    m_operationLayout->addStretch();
    // 设置操作区域占据1/3的空间
    m_mainLayout->addWidget(m_operationFrame, 1);
}

void TaskPage::setupQuestionSection()
{
    // 右侧主要区域
    m_rightFrame = new QFrame();
    m_rightLayout = new QVBoxLayout(m_rightFrame);
    m_rightLayout->setContentsMargins(15, 15, 15, 15);
    m_rightLayout->setSpacing(15);

    // 题目区域
    m_questionFrame = new QFrame();
    m_questionFrame->setMinimumHeight(200);
    m_questionLayout = new QVBoxLayout(m_questionFrame);
    m_questionLayout->setContentsMargins(15, 15, 15, 15);
    m_questionLayout->setSpacing(10);

    // 题目标题和清空按钮
    m_questionHeaderLayout = new QHBoxLayout();

    m_questionTitleLabel = new QLabel("推理测试：通用图像识别");
    m_questionTitleLabel->setObjectName("titleLabel");
    m_questionHeaderLayout->addWidget(m_questionTitleLabel);

    m_questionHeaderLayout->addStretch();

    // 清空按钮
    m_clearButton = new QPushButton("清空");
    m_clearButton->setFixedSize(60, 30);
    m_clearButton->setStyleSheet(
        "QPushButton {"
        "    background-color: #dc3545;"
        "    color: white;"
        "    border: 1px solid #c82333;"
        "    border-radius: 4px;"
        "    font-size: 12px;"
        "    font-weight: bold;"
        "}"
        "QPushButton:hover {"
        "    background-color: #c82333;"
        "}"
        "QPushButton:pressed {"
        "    background-color: #bd2130;"
        "}"
    );
    m_clearButton->setToolTip("清空当前题目的所有信息");
    m_questionHeaderLayout->addWidget(m_clearButton);

    m_questionLayout->addLayout(m_questionHeaderLayout);

    // 题目内容
    m_questionTextLabel = new QLabel("测试1：请对图像内容进行识别和分析。");
    m_questionTextLabel->setWordWrap(true);
    m_questionTextLabel->setStyleSheet("color: #000000; font-size: 14px; font-weight: normal;");
    m_questionLayout->addWidget(m_questionTextLabel);

    // 选择题选项区域 - 增加间距避免重叠
    m_choicesFrame = new QFrame();
    m_choicesLayout = new QVBoxLayout(m_choicesFrame);
    m_choicesLayout->setContentsMargins(15, 15, 15, 15);
    m_choicesLayout->setSpacing(12);  // 增加选项间距
    m_questionLayout->addWidget(m_choicesFrame);

    m_questionLayout->addStretch();
    m_rightLayout->addWidget(m_questionFrame);

    m_mainLayout->addWidget(m_rightFrame, 1);
}

void TaskPage::setupImageSection()
{
    // 按钮区域
    m_buttonLayout = new QHBoxLayout();

    m_takePhotoButton = new QPushButton("拍照");
    m_takePhotoButton->setMinimumHeight(40);
    m_buttonLayout->addWidget(m_takePhotoButton);

    // 上传图片按钮 - 缩小尺寸
    m_uploadButton = new QPushButton("上传");
    m_uploadButton->setMaximumWidth(80);
    m_uploadButton->setMinimumHeight(30);
    m_uploadButton->setStyleSheet("font-size: 12px; padding: 4px 8px;");
    m_buttonLayout->addWidget(m_uploadButton);

    m_inferenceButton = new QPushButton("推理参数");
    m_inferenceButton->setMinimumHeight(40);
    m_buttonLayout->addWidget(m_inferenceButton);

    m_rk3588InferenceButton = new QPushButton("开始推理");
    m_rk3588InferenceButton->setMinimumHeight(40);
    m_rk3588InferenceButton->setStyleSheet("background-color: #4CAF50; color: white;");
    m_buttonLayout->addWidget(m_rk3588InferenceButton);

    m_rightLayout->addLayout(m_buttonLayout);

    // 图片和推理结果区域
    m_contentLayout = new QHBoxLayout();

    // 图片预览区域
    m_imageFrame = new QFrame();
    m_imageFrame->setMinimumSize(300, 250);
    m_imageLayout = new QVBoxLayout(m_imageFrame);
    m_imageLayout->setContentsMargins(10, 10, 10, 10);

    m_imageAreaLabel = new QLabel("图片预览区");
    m_imageAreaLabel->setAlignment(Qt::AlignCenter);
    m_imageAreaLabel->setStyleSheet("font-weight: bold; color: #666;");
    m_imageLayout->addWidget(m_imageAreaLabel);

    m_imageLabel = new QLabel("点击拍照按钮获取图片");
    m_imageLabel->setAlignment(Qt::AlignCenter);
    m_imageLabel->setMinimumSize(280, 200);
    m_imageLabel->setStyleSheet("border: 2px dashed #ccc; border-radius: 8px; color: #888;");
    m_imageLayout->addWidget(m_imageLabel);

    m_contentLayout->addWidget(m_imageFrame);

    // 推理结果区域
    m_inferenceFrame = new QFrame();
    m_inferenceFrame->setMinimumSize(300, 250);
    m_inferenceLayout = new QVBoxLayout(m_inferenceFrame);
    m_inferenceLayout->setContentsMargins(10, 10, 10, 10);

    m_inferenceAreaLabel = new QLabel("模型输出区域");
    m_inferenceAreaLabel->setAlignment(Qt::AlignCenter);
    m_inferenceAreaLabel->setStyleSheet("font-weight: bold; color: #000000;");
    m_inferenceLayout->addWidget(m_inferenceAreaLabel);

    m_inferenceResultEdit = new QTextEdit();
    m_inferenceResultEdit->setStyleSheet("color: #000000; background-color: #ffffff; border: 1px solid #ccc;");
    m_inferenceResultEdit->setPlaceholderText("模型推理结果将在此显示");
    m_inferenceResultEdit->setMinimumHeight(200);
    m_inferenceLayout->addWidget(m_inferenceResultEdit);

    m_contentLayout->addWidget(m_inferenceFrame);

    m_rightLayout->addLayout(m_contentLayout);
}

void TaskPage::setupInferenceSection()
{
    // 删除模型测试按钮，只保留进度条
    // 进度条
    m_progressBar = new QProgressBar();
    m_progressBar->setVisible(false);
    m_progressBar->setMinimumHeight(25);
    m_rightLayout->addWidget(m_progressBar);

    m_progressLabel = new QLabel("准备就绪");
    m_progressLabel->setAlignment(Qt::AlignCenter);
    m_progressLabel->setStyleSheet("color: #666666;");
    m_rightLayout->addWidget(m_progressLabel);
}

// 加载题目数据
void TaskPage::loadQuestions(const QString& jsonFilePath)
{
    QFile file(jsonFilePath);
    if (!file.open(QIODevice::ReadOnly)) {
        QString errorMsg = QString("无法打开题目文件: %1").arg(jsonFilePath);
        QMessageBox::warning(this, "错误", errorMsg);
        return;
    }

    QByteArray data = file.readAll();
    QJsonDocument doc = QJsonDocument::fromJson(data);

    if (!doc.isArray()) {
        QString errorMsg = "题目文件格式错误";
        QMessageBox::warning(this, "错误", errorMsg);
        return;
    }

    QJsonArray questionsArray = doc.array();
    m_questions.clear();

    for (const auto& questionValue : questionsArray) {
        if (questionValue.isObject()) {
            QuestionData question = QuestionData::fromJson(questionValue.toObject());
            m_questions.append(question);
        }
    }

    if (!m_questions.isEmpty()) {
        m_currentQuestionIndex = 0;
        displayCurrentQuestion();
    } else {
        loadDefaultQuestions();
    }
}

// 加载默认题目（当文件加载失败时使用）
void TaskPage::loadDefaultQuestions()
{
    qDebug() << "Loading default questions";

    m_questions.clear();

    // 创建默认题目
    QuestionData q1;
    q1.question = "图中是什么物品？只需返回正确的选项编号。\nA. 物品A\nB. 物品B\nC. 物品C\nD. 物品D";
    q1.answer = "B";
    q1.questionType = "choice";
    q1.imagePaths << "default_image.jpg";
    m_questions.append(q1);

    QuestionData q2;
    q2.question = QString::fromUtf8("请回答以下判断题，你只需要输出错误或是正确\n题目如下：图中物品具有特定属性。");
    q2.answer = QString::fromUtf8("错误");
    q2.questionType = "judgment";
    q2.imagePaths << "default_image.jpg";
    m_questions.append(q2);

    QuestionData q3;
    q3.question = "以下哪个是该物品的主要特征？\nA. 特征A\nB. 特征B\nC. 特征C\nD. 特征D";
    q3.answer = "B";
    q3.questionType = "choice";
    q3.imagePaths << "default_image.jpg";
    m_questions.append(q3);

    qDebug() << "Loaded" << m_questions.size() << "default questions";

    if (!m_questions.isEmpty()) {
        m_currentQuestionIndex = 0;
        displayCurrentQuestion();
    }
}

void TaskPage::connectSignals()
{
    // 按钮信号
    connect(m_takePhotoButton, &QPushButton::clicked, this, &TaskPage::onTakePhoto);
    connect(m_uploadButton, &QPushButton::clicked, this, &TaskPage::onUploadImage);
    connect(m_inferenceButton, &QPushButton::clicked, this, &TaskPage::onShowInferenceParameters);
    connect(m_rk3588InferenceButton, &QPushButton::clicked, this, &TaskPage::onStartRK3588Inference);
    connect(m_clearButton, &QPushButton::clicked, this, &TaskPage::onClearCurrentQuestion);

    // 选择题答案信号
    connect(m_choicesButtonGroup, QOverload<QAbstractButton*>::of(&QButtonGroup::buttonClicked),
            this, &TaskPage::onAnswerSelected);

    // 模型推理信号
    connect(m_modelInference, &ModelInference::inferenceCompleted,
            this, &TaskPage::onInferenceCompleted);
    connect(m_modelInference, &ModelInference::inferenceError,
            this, &TaskPage::onInferenceError);

    // 相机信号已移除 - 不再使用CameraWidget UI组件
}

void TaskPage::onTakePhoto()
{
    // 检查是否有可用的摄像头设备，如果没有则重新检测
    if (m_availableCameraDevice.isEmpty()) {
        qDebug() << "[调试] 当前没有可用摄像头，尝试重新检测...";
        detectAvailableCamera();

        if (m_availableCameraDevice.isEmpty()) {
            qWarning() << "[调试] 重新检测后仍然没有可用的摄像头设备";
            emit taskError("没有检测到可用的摄像头设备，请检查设备连接");
            return;
        }
    }

    // 使用ffmpeg直接拍照，不依赖CameraWidget UI组件
    QString timestamp = QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss");
    QString fileName = QString("captured_%1.jpg").arg(timestamp);
    QString imagePath = QString("/opt/inference-test-app/saved_images/%1").arg(fileName);

    // 确保目录存在
    QDir().mkpath("/opt/inference-test-app/saved_images");

    // 使用检测到的摄像头设备进行拍照
    QString cmd = QString("ffmpeg -f v4l2 -video_size 640x480 -framerate 30 -pixel_format yuyv422 -i %1 -vf 'transpose=2,transpose=2' -pix_fmt yuvj420p -vframes 1 -y %2")
                      .arg(m_availableCameraDevice)
                      .arg(imagePath);

    qDebug() << "[调试] 使用摄像头设备:" << m_availableCameraDevice;
    qDebug() << "[调试] 拍照命令:" << cmd;

    QProcess *process = new QProcess(this);
    connect(process, QOverload<int, QProcess::ExitStatus>::of(&QProcess::finished),
            [this, process, imagePath](int exitCode, QProcess::ExitStatus exitStatus) {
                Q_UNUSED(exitStatus)
                if (exitCode == 0 && QFile::exists(imagePath)) {
                    qDebug() << "[调试] 拍照成功:" << imagePath;
                    loadImage(imagePath);
                } else {
                    qDebug() << "[调试] 拍照失败，退出码:" << exitCode;
                    emit taskError("拍照失败，请检查摄像头连接");
                }
                process->deleteLater();
            });

    // 启动拍照进程
    process->start("/bin/sh", QStringList() << "-c" << cmd);

    if (!process->waitForStarted(2000)) {
        qDebug() << "[调试] 拍照进程启动失败";
        emit taskError("拍照进程启动失败");
        process->deleteLater();
    }
}

void TaskPage::onUploadImage()
{
    // 创建文件对话框并设置样式
    QFileDialog dialog(this);
    dialog.setWindowTitle("选择药盒图片");
    dialog.setDirectory(QStandardPaths::writableLocation(QStandardPaths::PicturesLocation));
    dialog.setNameFilter("图片文件 (*.png *.jpg *.jpeg *.bmp *.gif)");
    dialog.setFileMode(QFileDialog::ExistingFile);
    dialog.setAcceptMode(QFileDialog::AcceptOpen);

    // 设置文件对话框样式，确保文字可见
    dialog.setStyleSheet(
        "QFileDialog {"
        "    color: #000000;"
        "    background-color: #ffffff;"
        "}"
        "QListView, QTreeView {"
        "    color: #000000;"
        "    background-color: #ffffff;"
        "    selection-background-color: #0078d4;"
        "    selection-color: #ffffff;"
        "}"
        "QLabel {"
        "    color: #000000;"
        "}"
        "QPushButton {"
        "    color: #000000;"
        "    background-color: #f0f0f0;"
        "    border: 1px solid #ccc;"
        "    padding: 4px 8px;"
        "}"
        "QPushButton:hover {"
        "    background-color: #e0e0e0;"
        "}"
        "QLineEdit {"
        "    color: #000000;"
        "    background-color: #ffffff;"
        "    border: 1px solid #ccc;"
        "}"
    );

    QString fileName;
    if (dialog.exec() == QDialog::Accepted) {
        QStringList selectedFiles = dialog.selectedFiles();
        if (!selectedFiles.isEmpty()) {
            fileName = selectedFiles.first();
        }
    }

    if (!fileName.isEmpty()) {
        loadImage(fileName);
    }
}

void TaskPage::onStartInference()
{
    if (!m_hasImage) {
        QMessageBox::warning(this, "提示", "请先拍照获取图片");
        return;
    }

    callInferenceModel("question");
}

void TaskPage::onStartRK3588Inference()
{
    if (!m_hasImage) {
        QMessageBox::warning(this, "提示", "请先拍照获取图片");
        return;
    }

    // 使用新的RK3588推理逻辑
    callRK3588DirectInference();
}



void TaskPage::onAnswerSelected()
{
    QRadioButton* selectedButton = qobject_cast<QRadioButton*>(m_choicesButtonGroup->checkedButton());
    if (selectedButton) {
        QString answer = selectedButton->text().left(1); // 获取A、B、C、D
        emit questionAnswered(answer);
        qDebug() << "Answer selected:" << answer;

        // 立即保存当前状态
        saveCurrentQuestionState();
    }
}

void TaskPage::onInferenceCompleted(const InferenceResult& result)
{
    // 确保在主线程中执行UI更新
    if (QThread::currentThread() != this->thread()) {
        QMetaObject::invokeMethod(this, "onInferenceCompleted", Qt::QueuedConnection,
                                  Q_ARG(InferenceResult, result));
        return;
    }

    m_isInferencing = false;
    showInferenceProgress(false);

    if (result.success) {
        // 获取当前题目的标准答案
        QString standardAnswer = "";
        if (m_currentQuestionIndex >= 0 && m_currentQuestionIndex < m_questions.size()) {
            const QuestionData& question = m_questions[m_currentQuestionIndex];
            standardAnswer = question.answer;
        }

        // 构建完整的显示文本，包含推理结果和标准答案
        QString displayText = result.getFormattedInstructions();

        // 如果有标准答案，添加到显示文本中
        if (!standardAnswer.isEmpty()) {
            displayText += "\n\n标准答案：" + standardAnswer;
        }

        // 安全地更新UI组件
        if (m_inferenceResultEdit) {
            m_inferenceResultEdit->setPlainText(displayText);
        }
    } else {
        if (m_inferenceResultEdit) {
            m_inferenceResultEdit->setPlainText("推理失败: " + result.errorMessage);
        }
    }

    // 立即保存当前状态
    saveCurrentQuestionState();

    emit inferenceFinished(result);
}

void TaskPage::setModelManager(ModelManager* modelManager)
{
    // 断开旧的连接
    if (m_modelManager) {
        m_modelManager->disconnect(this);
    }

    m_modelManager = modelManager;

    if (m_modelManager) {
        // 连接模型管理器的信号
        connect(m_modelManager, &ModelManager::inferenceCompleted,
                this, [this](const QString& result) {
                    // 处理推理结果
                    InferenceResult inferenceResult;
                    inferenceResult.success = true;
                    inferenceResult.analysisResult = result;
                    inferenceResult.confidence = 0.90f;
                    inferenceResult.detectedItem = "RK3588推理结果";

                    onInferenceCompleted(inferenceResult);
                });

        connect(m_modelManager, &ModelManager::inferenceError,
                this, [this](const QString& error) {
                    onInferenceError(error);
                });

        qDebug() << "TaskPage: ModelManager已设置并连接信号";
    } else {
        qDebug() << "TaskPage: ModelManager已清空";
    }
}

// 显示当前题目
void TaskPage::displayCurrentQuestion()
{
    if (m_currentQuestionIndex >= 0 && m_currentQuestionIndex < m_questions.size()) {
        const QuestionData& question = m_questions[m_currentQuestionIndex];

        // 更新题目标题
        m_questionTitleLabel->setText(QString("推理测试：通用图像识别"));

        // 更新题目内容
        QString questionText = question.getQuestionText();
        m_questionTextLabel->setText(questionText);

        // 清空之前的选项
        QLayoutItem* item;
        while ((item = m_choicesLayout->takeAt(0)) != nullptr) {
            delete item->widget();
            delete item;
        }

        // 添加新的选项
        if (question.questionType == "choice") {
            QStringList choices = question.getChoices();
            qDebug() << "Adding" << choices.size() << "choices";
            for (const QString& choice : choices) {
                QRadioButton* radioButton = new QRadioButton(choice);
                radioButton->setStyleSheet(
                    "QRadioButton {"
                    "    color: white;"
                    "    background-color: #0078d4;"
                    "    border: 1px solid #005a9e;"
                    "    border-radius: 6px;"
                    "    padding: 8px 16px;"
                    "    font-size: 14px;"
                    "    font-weight: bold;"
                    "    margin: 4px;"
                    "    min-height: 12px;"
                    "}"
                    "QRadioButton::indicator {"
                    "    width: 20px;"
                    "    height: 20px;"
                    "    border-radius: 10px;"
                    "    border: 2px solid white;"
                    "    background-color: transparent;"
                    "    margin-right: 8px;"
                    "}"
                    "QRadioButton::indicator:checked {"
                    "    background-color: white;"
                    "    border: 2px solid white;"
                    "}"
                    "QRadioButton:hover {"
                    "    background-color: #106ebe;"
                    "}"
                );
                m_choicesButtonGroup->addButton(radioButton);
                m_choicesLayout->addWidget(radioButton);
                qDebug() << "Added choice:" << choice;
            }
        } else if (question.questionType == "judgment") {
            qDebug() << "Adding judgment options";
            QRadioButton* trueButton = new QRadioButton("正确");
            QRadioButton* falseButton = new QRadioButton("错误");

            QString radioButtonStyle =
                "QRadioButton {"
                "    color: white;"
                "    background-color: #0078d4;"
                "    border: 1px solid #005a9e;"
                "    border-radius: 6px;"
                "    padding: 12px 16px;"
                "    font-size: 14px;"
                "    font-weight: bold;"
                "    margin: 4px;"
                "    min-height: 20px;"
                "}"
                "QRadioButton::indicator {"
                "    width: 20px;"
                "    height: 20px;"
                "    border-radius: 10px;"
                "    border: 2px solid white;"
                "    background-color: transparent;"
                "    margin-right: 8px;"
                "}"
                "QRadioButton::indicator:checked {"
                "    background-color: white;"
                "    border: 2px solid white;"
                "}"
                "QRadioButton:hover {"
                "    background-color: #106ebe;"
                "}";

            trueButton->setStyleSheet(radioButtonStyle);
            falseButton->setStyleSheet(radioButtonStyle);

            m_choicesButtonGroup->addButton(trueButton);
            m_choicesButtonGroup->addButton(falseButton);
            m_choicesLayout->addWidget(trueButton);
            m_choicesLayout->addWidget(falseButton);
        }
    } else {
        qDebug() << "No valid question to display";
    }
}



void TaskPage::onInferenceError(const QString& errorMessage)
{
    // 确保在主线程中执行UI更新
    if (QThread::currentThread() != this->thread()) {
        QMetaObject::invokeMethod(this, "onInferenceError", Qt::QueuedConnection,
                                  Q_ARG(QString, errorMessage));
        return;
    }

    m_isInferencing = false;
    showInferenceProgress(false);

    // 安全地更新UI组件
    if (m_inferenceResultEdit) {
        m_inferenceResultEdit->setPlainText("推理错误: " + errorMessage);
    }
}

// 调用推理模型
void TaskPage::callInferenceModel(const QString& modelType)
{
    m_isInferencing = true;
    showInferenceProgress(true);

#ifdef RK3588_PLATFORM
    // 在RK3588平台上使用真实的模型推理
    Q_UNUSED(modelType)  // RK3588平台不使用modelType参数
    m_progressLabel->setText("正在进行RK3588模型推理...");
    m_modelInference->startRK3588Inference(m_currentImagePath);
#else
    // 在其他平台上使用模拟推理
    QString modelPath;
    QString resultText;

    if (modelType == "host") {
        // 主机模型路径 - autobit_back/yolo-rk3588/models/
        modelPath = "../autobit_back/yolo-rk3588/models/yolov8n.onnx";
        resultText = "调用主机YOLO模型进行图像识别...";
        m_progressLabel->setText("正在使用主机模型推理...");
    } else if (modelType == "rk3588") {
        // RK3588模型路径 - autobit_back/yolo-rk3588/models/
        modelPath = "../autobit_back/yolo-rk3588/models/yolov8n.rknn";
        resultText = "调用RK3588 YOLO模型进行图像识别...";
        m_progressLabel->setText("正在使用RK3588模型推理...");
    } else {
        // 通用推理模型路径（默认）
        modelPath = "/path/to/inference/model";
        resultText = "调用通用推理模型...";
        m_progressLabel->setText("正在进行图像推理...");
    }

    // 直接报错，不提供模拟推理
    onInferenceError("推理模型不可用，请检查模型配置和文件路径");
#endif
}

void TaskPage::callRK3588DirectInference()
{
    // 检查模型管理器是否可用
    if (!m_modelManager) {
        onInferenceError("模型管理器未初始化");
        return;
    }

    if (!m_modelManager->isModelLoaded()) {
        onInferenceError("模型未加载，请等待模型加载完成");
        return;
    }

    m_isInferencing = true;
    showInferenceProgress(true);
    m_progressLabel->setText("正在进行RK3588推理...");

    QString imagePath = m_currentImagePath;

    qDebug() << "开始推理:" << imagePath;

    // 获取当前题目内容并构建提示文本
    QString questionText = "";
    if (m_currentQuestionIndex >= 0 && m_currentQuestionIndex < m_questions.size()) {
        const QuestionData& question = m_questions[m_currentQuestionIndex];
        questionText = QString("请仔细观察图片并回答以下问题。\n\n题目：%1\n\n请严格按照以下格式回答，必须包含分析和答案两个部分：\n\n分析：（请详细分析图片内容，说明你的推理过程）\n\n答案：（请给出最终答案，如果是选择题请给出选项字母，如果是判断题请回答'正确'或'错误'）").arg(question.question);
    } else {
        questionText = "请分析图片内容。\n\n请严格按照以下格式回答：\n\n分析：（请详细分析图片内容）\n\n答案：（请给出分析结论）";
    }



    // 发送推理请求到模型管理器
    m_modelManager->sendInferenceRequest(imagePath, questionText);

}

// 获取当前题目
QuestionData TaskPage::getCurrentQuestion() const
{
    if (m_currentQuestionIndex >= 0 && m_currentQuestionIndex < m_questions.size()) {
        return m_questions[m_currentQuestionIndex];
    }
    return QuestionData();
}

// 题目导航方法
void TaskPage::goToNextQuestion()
{
    if (hasNextQuestion()) {
        // 保存当前题目状态
        saveCurrentQuestionState();

        m_currentQuestionIndex++;
        displayCurrentQuestion();

        // 恢复新题目的状态
        restoreQuestionState(m_currentQuestionIndex);

        emit questionChanged(m_currentQuestionIndex, m_questions.size());
        qDebug() << "Moved to next question:" << m_currentQuestionIndex;
    }
}

void TaskPage::goToPreviousQuestion()
{
    if (hasPreviousQuestion()) {
        // 保存当前题目状态
        saveCurrentQuestionState();

        m_currentQuestionIndex--;
        displayCurrentQuestion();

        // 恢复新题目的状态
        restoreQuestionState(m_currentQuestionIndex);

        emit questionChanged(m_currentQuestionIndex, m_questions.size());
        qDebug() << "Moved to previous question:" << m_currentQuestionIndex;
    }
}

bool TaskPage::hasNextQuestion() const
{
    return m_currentQuestionIndex < m_questions.size() - 1;
}

bool TaskPage::hasPreviousQuestion() const
{
    return m_currentQuestionIndex > 0;
}

void TaskPage::updateInferenceProgress()
{
    if (m_isInferencing && m_progressTimer) {
        m_progressValue = (m_progressValue + 5) % 100;
        m_progressBar->setValue(m_progressValue);
    }
}

void TaskPage::loadImage(const QString& imagePath)
{
    QImageReader reader(imagePath);
    if (!reader.canRead()) {
        emit taskError("无法读取图片文件: " + imagePath);
        return;
    }
    
    QImage image = reader.read();
    if (image.isNull()) {
        emit taskError("图片文件损坏或格式不支持");
        return;
    }
    
    // 缩放图片以适应显示区域
    QPixmap pixmap = QPixmap::fromImage(image);
    displayImage(pixmap);
    
    m_currentImagePath = imagePath;
    m_hasImage = true;
    
    QFileInfo fileInfo(imagePath);
    // 更新图片区域标签显示文件信息
    QString fileInfoText = QString("已选择: %1 (%2)")
                           .arg(fileInfo.fileName())
                           .arg(fileInfo.size() > 1024*1024 ?
                                QString("%1 MB").arg(fileInfo.size()/1024.0/1024.0, 0, 'f', 1) :
                                QString("%1 KB").arg(fileInfo.size()/1024.0, 0, 'f', 1));
    m_imageAreaLabel->setText(fileInfoText);
    
    emit imageUploaded(imagePath);

    // 立即保存当前状态
    saveCurrentQuestionState();

    qDebug() << "Image loaded:" << imagePath;
}

void TaskPage::displayImage(const QPixmap& pixmap)
{
    QPixmap scaledPixmap = pixmap.scaled(
        IMAGE_DISPLAY_WIDTH, IMAGE_DISPLAY_HEIGHT,
        Qt::KeepAspectRatio, Qt::SmoothTransformation
    );
    
    m_imageLabel->setPixmap(scaledPixmap);
    m_imageLabel->setStyleSheet("border: 2px solid #0078d4; border-radius: 8px;");
}

void TaskPage::showInferenceProgress(bool show)
{
    // 安全地更新UI组件，添加空指针检查
    if (m_progressBar) {
        m_progressBar->setVisible(show);
    }
    if (m_progressLabel) {
        m_progressLabel->setVisible(show);
    }
    if (m_inferenceButton) {
        m_inferenceButton->setEnabled(!show);
    }
    if (m_rk3588InferenceButton) {
        m_rk3588InferenceButton->setEnabled(!show);
    }

    if (show) {
        m_progressValue = 0;
        if (m_progressBar) {
            m_progressBar->setValue(0);
        }
        if (m_progressLabel) {
            m_progressLabel->setText("正在初始化推理...");
        }

        // 启动进度更新定时器
        if (!m_progressTimer) {
            m_progressTimer = new QTimer(this);
            connect(m_progressTimer, &QTimer::timeout, this, &TaskPage::updateInferenceProgress);
        }
        m_progressTimer->start(200); // 每200ms更新一次
    } else {
        if (m_progressTimer) {
            m_progressTimer->stop();
        }
        if (m_progressBar) {
            m_progressBar->setValue(100);
        }
        if (m_progressLabel) {
            m_progressLabel->setText("推理完成");
        }
    }
}

void TaskPage::resetPage()
{
    // 清空图片
    m_currentImagePath.clear();
    m_hasImage = false;
    m_imageLabel->clear();
    m_imageLabel->setText("点击拍照按钮获取图片");
    m_imageLabel->setStyleSheet("border: 2px dashed #ccc; border-radius: 8px; color: #888;");

    // 清空推理结果
    m_inferenceResultEdit->clear();

    // 重置题目到第一题
    if (!m_questions.isEmpty()) {
        m_currentQuestionIndex = 0;
        displayCurrentQuestion();
    }
}

void TaskPage::setPageEnabled(bool enabled)
{
    setEnabled(enabled);
}

void TaskPage::dragEnterEvent(QDragEnterEvent *event)
{
    if (event->mimeData()->hasUrls()) {
        QList<QUrl> urls = event->mimeData()->urls();
        if (!urls.isEmpty()) {
            QString fileName = urls.first().toLocalFile();
            QStringList supportedFormats = {"png", "jpg", "jpeg", "bmp", "gif"};
            QString suffix = QFileInfo(fileName).suffix().toLower();
            
            if (supportedFormats.contains(suffix)) {
                event->acceptProposedAction();
                return;
            }
        }
    }
    event->ignore();
}

void TaskPage::dropEvent(QDropEvent *event)
{
    QList<QUrl> urls = event->mimeData()->urls();
    if (!urls.isEmpty()) {
        QString fileName = urls.first().toLocalFile();
        loadImage(fileName);
        event->acceptProposedAction();
    }
}

// 状态保存和恢复方法
void TaskPage::saveCurrentQuestionState()
{
    if (m_currentQuestionIndex >= 0 && m_currentQuestionIndex < m_questions.size()) {
        QuestionState& state = m_questionStates[m_currentQuestionIndex];

        // 保存图片信息
        state.imagePath = m_currentImagePath;
        state.hasImage = m_hasImage;
        if (m_hasImage && m_imageLabel->pixmap() && !m_imageLabel->pixmap()->isNull()) {
            state.imagePixmap = *m_imageLabel->pixmap();
        }

        // 保存推理结果
        state.inferenceResult = m_inferenceResultEdit->toPlainText();

        // 保存选择的答案
        QAbstractButton* checkedButton = m_choicesButtonGroup->checkedButton();
        if (checkedButton) {
            state.selectedAnswer = checkedButton->text();
        } else {
            state.selectedAnswer.clear();
        }

        qDebug() << "Saved state for question" << m_currentQuestionIndex
                 << "- hasImage:" << state.hasImage
                 << "- answer:" << state.selectedAnswer;
    }
}

void TaskPage::restoreQuestionState(int questionIndex)
{
    if (m_questionStates.contains(questionIndex)) {
        const QuestionState& state = m_questionStates[questionIndex];

        // 恢复图片
        if (state.hasImage && !state.imagePixmap.isNull()) {
            m_currentImagePath = state.imagePath;
            m_hasImage = state.hasImage;
            m_imageLabel->setPixmap(state.imagePixmap);
            m_imageLabel->setStyleSheet("border: 2px solid #0078d4; border-radius: 8px;");

            // 更新图片区域标签
            QFileInfo fileInfo(state.imagePath);
            QString fileInfoText = QString("已选择: %1").arg(fileInfo.fileName());
            m_imageAreaLabel->setText(fileInfoText);
        } else {
            // 清空图片显示
            m_currentImagePath.clear();
            m_hasImage = false;
            m_imageLabel->clear();
            m_imageLabel->setText("点击拍照按钮获取图片");
            m_imageLabel->setStyleSheet("border: 2px dashed #ccc; border-radius: 8px; color: #888;");
            m_imageAreaLabel->setText("图片预览区");
        }

        // 恢复推理结果
        m_inferenceResultEdit->setPlainText(state.inferenceResult);

        // 恢复选择的答案
        if (!state.selectedAnswer.isEmpty()) {
            QList<QAbstractButton*> buttons = m_choicesButtonGroup->buttons();
            for (QAbstractButton* button : buttons) {
                if (button->text() == state.selectedAnswer) {
                    button->setChecked(true);
                    break;
                }
            }
        } else {
            // 清空所有选择
            QAbstractButton* checkedButton = m_choicesButtonGroup->checkedButton();
            if (checkedButton) {
                checkedButton->setChecked(false);
            }
        }

        qDebug() << "Restored state for question" << questionIndex
                 << "- hasImage:" << state.hasImage
                 << "- answer:" << state.selectedAnswer;
    } else {
        // 没有保存的状态，清空所有内容
        clearCurrentQuestionState();
    }
}

void TaskPage::clearCurrentQuestionState()
{
    // 清空图片
    m_currentImagePath.clear();
    m_hasImage = false;
    m_imageLabel->clear();
    m_imageLabel->setText("点击拍照按钮获取图片");
    m_imageLabel->setStyleSheet("border: 2px dashed #ccc; border-radius: 8px; color: #888;");
    m_imageAreaLabel->setText("图片预览区");

    // 清空推理结果
    m_inferenceResultEdit->clear();

    // 清空选择的答案
    QAbstractButton* checkedButton = m_choicesButtonGroup->checkedButton();
    if (checkedButton) {
        checkedButton->setChecked(false);
    }

    qDebug() << "Cleared state for current question";
}

void TaskPage::onClearCurrentQuestion()
{
    int ret = QMessageBox::question(this, "确认清空",
                                   "确定要清空当前题目的所有信息吗？\n（包括图片、推理结果和选择的答案）",
                                   QMessageBox::Yes | QMessageBox::No,
                                   QMessageBox::No);

    if (ret == QMessageBox::Yes) {
        // 清空当前显示的内容
        clearCurrentQuestionState();

        // 删除保存的状态
        if (m_questionStates.contains(m_currentQuestionIndex)) {
            m_questionStates.remove(m_currentQuestionIndex);
        }

        qDebug() << "User cleared question" << m_currentQuestionIndex;
    }
}

void TaskPage::onShowInferenceParameters()
{
    // 从配置文件读取推理参数信息
    ConfigManager* config = ConfigManager::instance();
    QString demoPath = config->getRK3588DemoPath();
    QString imagePath = m_hasImage ? m_currentImagePath : "[未选择图片]";
    QString rknnModel = config->getRK3588RknnModelPath();
    QString rkllmModel = config->getRK3588RkllmModelPath();
    QString maxTokens = QString::number(config->getRK3588MaxTokens());
    QString contextLength = QString::number(config->getRK3588ContextLength());
    QString corenum = QString::number(config->getRK3588CoreNum());

    QString parameterInfo = QString(
        "推理参数配置\n"
        "===========================================\n\n"
        "执行程序: %1\n\n"
        "参数列表:\n"
        "  1. 图片路径: %2\n"
        "  2. RKNN模型: %3\n"
        "  3. RKLLM模型: %4\n"
        "  4. 最大令牌数: %5\n"
        "  5. 上下文长度: %6\n"
        "  6. 核心数量: %7\n\n"
        "完整命令:\n"
        "%1 \\\n"
        "  \"%2\" \\\n"
        "  \"%3\" \\\n"
        "  \"%4\" \\\n"
        "  %5 \\\n"
        "  %6 \\\n"
        "  %7\n\n"
        "说明:\n"
        "• 这是\"开始推理\"按钮实际调用的命令参数\n"
        "• 图片路径会根据当前选择的图片动态更新\n"
        "• 其他参数为系统预设的推理配置"
    ).arg(demoPath)
     .arg(QFileInfo(imagePath).fileName())  // 只显示文件名，避免路径过长
     .arg(QFileInfo(rknnModel).fileName())
     .arg(QFileInfo(rkllmModel).fileName())
     .arg(maxTokens)
     .arg(contextLength)
     .arg(corenum);

    // 创建消息框显示参数
    QMessageBox msgBox(this);
    msgBox.setWindowTitle("推理参数配置");
    msgBox.setText(parameterInfo);
    msgBox.setIcon(QMessageBox::Information);
    msgBox.setStandardButtons(QMessageBox::Ok);

    // 设置消息框样式，确保文字清晰可见
    msgBox.setStyleSheet(
        "QMessageBox {"
        "    background-color: #ffffff;"
        "    color: #000000;"
        "}"
        "QMessageBox QLabel {"
        "    color: #000000;"
        "    font-family: 'Consolas', 'Monaco', monospace;"
        "    font-size: 12px;"
        "    background-color: #ffffff;"
        "}"
        "QMessageBox QPushButton {"
        "    background-color: #0078d4;"
        "    color: #ffffff;"
        "    border: 1px solid #005a9e;"
        "    border-radius: 4px;"
        "    padding: 6px 12px;"
        "    min-width: 60px;"
        "}"
        "QMessageBox QPushButton:hover {"
        "    background-color: #106ebe;"
        "}"
    );

    msgBox.exec();
}

void TaskPage::detectAvailableCamera()
{
    // 首先使用系统命令列出所有video设备
    QStringList discoveredDevices = discoverVideoDevices();

    if (discoveredDevices.isEmpty()) {
        m_availableCameraDevice = "";
        return;
    }

    QString bestCandidate = "";
    int bestScore = 0;

    // 遍历所有设备，寻找目标RGB Camera
    for (const QString& device : discoveredDevices) {
        // 首先测试设备是否可用
        if (!testCameraDevice(device)) {
            continue;
        }

        // 检查匹配程度
        int matchScore = getCameraMatchScore(device);

        if (matchScore == 3) {
            // 完全匹配，直接选择
            m_availableCameraDevice = device;
            qDebug() << "找到完全匹配的目标RGB Camera:" << device;
            return;
        } else if (matchScore > bestScore) {
            // 记录最佳候选
            bestCandidate = device;
            bestScore = matchScore;
        }
    }

    // 如果没有完全匹配，但有部分匹配的候选
    if (!bestCandidate.isEmpty() && bestScore > 0) {
        m_availableCameraDevice = bestCandidate;
        qDebug() << "选择部分匹配的摄像头:" << bestCandidate << "(匹配分数:" << bestScore << ")";
        return;
    }

    // 如果完全没有匹配，记录警告但不选择任何设备
    qWarning() << "未找到任何匹配的RGB Camera，请检查摄像头连接";
    m_availableCameraDevice = ""; // 不选择任何设备
}

bool TaskPage::testCameraDevice(const QString& devicePath)
{
    // 使用ffmpeg快速测试设备是否可用（只获取1帧，不保存）
    QString testCmd = QString("timeout 3 ffmpeg -f v4l2 -video_size 640x480 -framerate 30 -i %1 -vframes 1 -f null - 2>/dev/null").arg(devicePath);

    QProcess testProcess;
    testProcess.start("/bin/sh", QStringList() << "-c" << testCmd);

    // 等待最多5秒
    if (!testProcess.waitForFinished(5000)) {
        testProcess.kill();
        return false;
    }

    int exitCode = testProcess.exitCode();
    // 退出码0表示成功
    return (exitCode == 0);
}

bool TaskPage::isTargetRGBCamera(const QString& devicePath)
{
    return getCameraMatchScore(devicePath) == 3;
}

int TaskPage::getCameraMatchScore(const QString& devicePath)
{
    // 使用v4l2-ctl检查设备信息，获取摄像头匹配分数
    QString infoCmd = QString("v4l2-ctl -d %1 --info 2>/dev/null").arg(devicePath);

    QProcess infoProcess;
    infoProcess.start("/bin/sh", QStringList() << "-c" << infoCmd);

    if (!infoProcess.waitForFinished(3000)) {
        infoProcess.kill();
        return 0;
    }

    QString output = infoProcess.readAllStandardOutput();

    // 检测特征
    bool hasRGBCameraName = output.contains("RGB Camera: RGB Camera");
    bool hasTargetVersion = output.contains("0x00000001");
    bool isKP26BLD = output.contains("KP26BLD");

    // 计算匹配分数
    int score = 0;

    // +2分：RGB Camera名称匹配（最重要的特征）
    if (hasRGBCameraName) {
        score += 2;
    }

    // +1分：硬件版本匹配
    if (hasTargetVersion) {
        score += 1;
    }

    // -1分：如果是KP26BLD系列，降低优先级
    if (isKP26BLD) {
        score -= 1;
    }

    // 记录检测结果
    qDebug() << "设备" << devicePath << "匹配分析:";
    qDebug() << "  RGB Camera名称:" << (hasRGBCameraName ? "是(+2)" : "否(+0)");
    qDebug() << "  硬件版本0x00000001:" << (hasTargetVersion ? "是(+1)" : "否(+0)");
    qDebug() << "  KP26BLD系列:" << (isKP26BLD ? "是(-1)" : "否(+0)");
    qDebug() << "  总分:" << score;

    // 分数说明：
    // 3分：完美匹配（RGB Camera + 硬件版本 + 非KP26BLD）
    // 2分：RGB Camera名称匹配，但硬件版本不匹配或是KP26BLD
    // 1分：仅硬件版本匹配
    // 0分或负分：不匹配或KP26BLD系列

    return score;
}

QStringList TaskPage::discoverVideoDevices()
{
    QStringList devices;

    // 方法1: 使用ls命令列出/dev/video*设备
    QProcess lsProcess;
    lsProcess.start("/bin/sh", QStringList() << "-c" << "ls /dev/video* 2>/dev/null");
    if (lsProcess.waitForFinished(2000)) {
        QString output = lsProcess.readAllStandardOutput().trimmed();
        if (!output.isEmpty()) {
            QStringList lsDevices = output.split('\n', Qt::SkipEmptyParts);
            for (const QString& device : lsDevices) {
                if (!devices.contains(device)) {
                    devices.append(device);
                }
            }
        }
    }

    // 方法2: 使用v4l2-ctl列出设备（如果可用）
    QProcess v4l2Process;
    v4l2Process.start("/bin/sh", QStringList() << "-c" << "v4l2-ctl --list-devices 2>/dev/null | grep -E '/dev/video[0-9]+' | tr -d '\\t'");
    if (v4l2Process.waitForFinished(3000)) {
        QString output = v4l2Process.readAllStandardOutput().trimmed();
        if (!output.isEmpty()) {
            QStringList v4l2Devices = output.split('\n', Qt::SkipEmptyParts);
            for (const QString& device : v4l2Devices) {
                QString cleanDevice = device.trimmed();
                if (!devices.contains(cleanDevice) && cleanDevice.startsWith("/dev/video")) {
                    devices.append(cleanDevice);
                }
            }
        }
    }

    // 方法3: 使用find命令查找video设备
    QProcess findProcess;
    findProcess.start("/bin/sh", QStringList() << "-c" << "find /dev -name 'video*' -type c 2>/dev/null");
    if (findProcess.waitForFinished(2000)) {
        QString output = findProcess.readAllStandardOutput().trimmed();
        if (!output.isEmpty()) {
            QStringList findDevices = output.split('\n', Qt::SkipEmptyParts);
            for (const QString& device : findDevices) {
                QString cleanDevice = device.trimmed();
                if (!devices.contains(cleanDevice)) {
                    devices.append(cleanDevice);
                }
            }
        }
    }

    // 方法4: 直接检查/dev目录中的video设备文件
    QDir devDir("/dev");
    QStringList videoFiles = devDir.entryList(QStringList() << "video*", QDir::System | QDir::Files);
    for (const QString& file : videoFiles) {
        QString fullPath = "/dev/" + file;
        if (!devices.contains(fullPath)) {
            devices.append(fullPath);
        }
    }

    // 按设备编号排序
    std::sort(devices.begin(), devices.end());

    return devices;
}

QString TaskPage::getAvailableCameraDevice() const
{
    return m_availableCameraDevice;
}
