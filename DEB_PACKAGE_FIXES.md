# .deb包卸载问题修复说明

## 问题描述

在使用 `sudo apt remove --purge diagnosis-app` 卸载软件时，出现卸载过程中断的问题：

```
正在卸载 diagnosis-app (1.0.0) ... 
[INFO] 准备卸载部署模型推理测试系统... 
[INFO] 停止应用程序进程... 
已终止————
```

## 问题分析

经过分析，问题主要出现在以下几个方面：

1. **prerm脚本过于简单**：原始的prerm脚本缺少详细的日志和错误处理
2. **进程停止逻辑不完善**：没有优雅停止和强制停止的区分
3. **缺少调试信息**：无法确定具体在哪个步骤失败
4. **错误处理不足**：脚本遇到错误时没有适当的处理机制

## 解决方案

### 1. 改进prerm脚本 (debian/prerm)

**主要改进：**
- 添加详细的日志输出
- 实现优雅停止 → 强制停止的两阶段进程终止
- 增加进程检查和验证
- 添加错误处理和调试信息

**新功能：**
- 显示发现的进程PID
- 分别处理DiagnosisApp和diagnosis-app进程
- 等待进程完全退出
- 最终验证所有进程已停止

### 2. 改进postrm脚本 (debian/postrm)

**主要改进：**
- 添加详细的日志输出
- 实现安全的文件/目录删除函数
- 分别处理remove和purge操作
- 增加错误处理和警告信息

**新功能：**
- 安全删除文件和目录的函数
- 详细的操作日志
- 用户和组的安全删除
- 桌面数据库更新

### 3. 更新build_deb.sh脚本

**主要改进：**
- 版本号更新到1.0.1
- 添加调试模式支持
- 改进的帮助信息
- 更好的错误处理

**新功能：**
- `--debug` 参数保留临时文件用于调试
- 更详细的构建日志
- 改进的使用说明

### 4. 新增测试脚本

**test_install_uninstall.sh：**
- 专门测试安装和卸载过程
- 详细的验证步骤
- 完整的日志记录
- 残留文件检查

## 使用方法

### 1. 重新构建.deb包

```bash
# 使用新的构建脚本
./build_deb.sh --clean

# 或者使用调试模式
./build_deb.sh --debug
```

### 2. 测试包的完整性

```bash
# 测试包结构和内容
./test_deb_package.sh

# 测试安装和卸载过程
chmod +x test_install_uninstall.sh
./test_install_uninstall.sh
```

### 3. 在目标设备上安装

```bash
# 传输到ARM64设备后安装
sudo dpkg -i diagnosis-app_1.0.1_arm64.deb
sudo apt-get install -f  # 解决依赖问题
```

### 4. 卸载测试

```bash
# 正常卸载
sudo apt remove --purge diagnosis-app

# 如果卸载有问题，使用清理脚本
sudo ./cleanup_system.sh
```

## 调试方法

### 1. 查看详细卸载日志

```bash
# 启用dpkg调试
sudo dpkg -r --debug=1 diagnosis-app

# 查看系统日志
journalctl -u dpkg -f
```

### 2. 手动测试脚本

```bash
# 测试prerm脚本
sudo /var/lib/dpkg/info/diagnosis-app.prerm remove

# 测试postrm脚本
sudo /var/lib/dpkg/info/diagnosis-app.postrm purge
```

### 3. 检查进程状态

```bash
# 查看相关进程
ps aux | grep -i diagnosis
pgrep -f "DiagnosisApp\|diagnosis-app"

# 手动停止进程
pkill -f "DiagnosisApp"
pkill -f "diagnosis-app"
```

## 主要改进点

### 1. 日志输出
- 所有操作都有详细的日志输出
- 使用颜色区分不同类型的消息
- 记录操作的成功/失败状态

### 2. 错误处理
- 每个操作都有适当的错误处理
- 失败时提供有用的调试信息
- 不会因为单个操作失败而中断整个过程

### 3. 进程管理
- 优雅停止 → 强制停止的两阶段方式
- 验证进程是否真正停止
- 处理僵尸进程和顽固进程

### 4. 文件清理
- 安全的文件删除函数
- 检查文件是否存在再删除
- 详细的清理状态报告

## 版本更新

- **v1.0.0** → **v1.0.1**
- 修复卸载中断问题
- 改进调试和日志功能
- 增强错误处理机制
- 添加完整的测试套件

## 故障排除

如果仍然遇到卸载问题：

1. **查看详细日志**：`journalctl -u dpkg`
2. **手动清理**：`sudo ./cleanup_system.sh`
3. **强制移除**：`sudo dpkg --remove --force-remove-reinstreq diagnosis-app`
4. **检查dpkg状态**：`sudo dpkg --configure -a`

## 测试结果

经过改进后，.deb包应该能够：
- ✅ 正常安装所有文件
- ✅ 正确设置权限和用户
- ✅ 优雅停止所有进程
- ✅ 完全清理所有文件
- ✅ 删除用户和组
- ✅ 更新系统集成

这些改进确保了.deb包的安装和卸载过程更加稳定和可靠。
