#!/bin/bash
# 诊断推理应用 .deb 包构建脚本
# 适用于 arm64 Debian 系统

set -e  # 遇到错误立即退出

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
echo_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
echo_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
echo_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_NAME="InferenceApp"
PACKAGE_NAME="inference-test-app"
VERSION="1.0.0"
ARCH="arm64"

# 目录配置
BUILD_DIR="${SCRIPT_DIR}/build"
PACKAGE_DIR="${SCRIPT_DIR}/debian"
DEB_BUILD_DIR="${SCRIPT_DIR}/deb_build"
DEB_ROOT="${DEB_BUILD_DIR}/${PACKAGE_NAME}_${VERSION}_${ARCH}"

echo_info "=== 模型推理测试系统 .deb 包构建脚本 ==="
echo_info "项目目录: ${SCRIPT_DIR}"
echo_info "构建目录: ${BUILD_DIR}"
echo_info "包名: ${PACKAGE_NAME}"
echo_info "版本: ${VERSION}"
echo_info "架构: ${ARCH}"

# 检查构建环境
check_build_environment() {
    echo_info "检查构建环境..."
    
    # 检查必要的工具
    local required_tools=("dpkg-deb" "fakeroot")
    for tool in "${required_tools[@]}"; do
        if ! command -v "$tool" >/dev/null 2>&1; then
            echo_error "缺少必要工具: $tool"
            echo_info "请安装: sudo apt install dpkg-dev fakeroot"
            exit 1
        fi
    done
    
    # 检查可执行文件是否存在
    if [[ ! -f "${BUILD_DIR}/${PROJECT_NAME}" ]]; then
        echo_error "可执行文件不存在: ${BUILD_DIR}/${PROJECT_NAME}"
        echo_info "请先运行 ./build.sh 编译项目"
        exit 1
    fi
    
    echo_success "构建环境检查完成"
}

# 清理之前的构建
clean_previous_build() {
    echo_info "清理之前的构建..."
    
    if [[ -d "${DEB_BUILD_DIR}" ]]; then
        rm -rf "${DEB_BUILD_DIR}"
    fi
    
    # 清理之前的 .deb 文件
    rm -f "${SCRIPT_DIR}"/*.deb
    
    echo_success "清理完成"
}

# 创建包目录结构
create_package_structure() {
    echo_info "创建包目录结构..."
    
    # 创建主目录结构
    mkdir -p "${DEB_ROOT}/opt/inference-test-app"
    mkdir -p "${DEB_ROOT}/opt/inference-test-app/saved_images"  # 图片保存目录
    mkdir -p "${DEB_ROOT}/etc/inference-test-app"
    mkdir -p "${DEB_ROOT}/usr/share/applications"
    mkdir -p "${DEB_ROOT}/usr/local/bin"
    
    # 复制 DEBIAN 控制文件
    cp -r "${PACKAGE_DIR}/DEBIAN" "${DEB_ROOT}/"
    
    # 设置控制文件权限
    chmod 755 "${DEB_ROOT}/DEBIAN/postinst"
    chmod 755 "${DEB_ROOT}/DEBIAN/prerm"
    chmod 755 "${DEB_ROOT}/DEBIAN/postrm"
    
    echo_success "包目录结构创建完成"
}

# 复制应用程序文件
copy_application_files() {
    echo_info "复制应用程序文件..."
    
    # 复制可执行文件
    cp "${BUILD_DIR}/${PROJECT_NAME}" "${DEB_ROOT}/opt/inference-test-app/"
    chmod +x "${DEB_ROOT}/opt/inference-test-app/${PROJECT_NAME}"

    # 复制启动脚本
    if [[ -f "${BUILD_DIR}/run_inference_app.sh" ]]; then
        cp "${BUILD_DIR}/run_inference_app.sh" "${DEB_ROOT}/opt/inference-test-app/"
        chmod +x "${DEB_ROOT}/opt/inference-test-app/run_inference_app.sh"
        echo_info "启动脚本复制完成"
    elif [[ -f "${BUILD_DIR}/run_diagnosis_app.sh" ]]; then
        # 兼容旧版本脚本名称
        cp "${BUILD_DIR}/run_diagnosis_app.sh" "${DEB_ROOT}/opt/inference-test-app/run_inference_app.sh"
        chmod +x "${DEB_ROOT}/opt/inference-test-app/run_inference_app.sh"
        echo_info "旧版启动脚本复制并重命名完成"
    fi

    # 复制配置文件
    if [[ -d "${SCRIPT_DIR}/config" ]]; then
        cp -r "${SCRIPT_DIR}/config" "${DEB_ROOT}/opt/inference-test-app/"
    fi

    # 复制示例配置文件到系统配置目录
    if [[ -f "${SCRIPT_DIR}/config/inference.conf.example" ]]; then
        cp "${SCRIPT_DIR}/config/inference.conf.example" "${DEB_ROOT}/opt/inference-test-app/config/"
    fi

    # 复制其他资源文件
    for dir in "models" "images" "icons" "data"; do
        if [[ -d "${BUILD_DIR}/${dir}" ]]; then
            cp -r "${BUILD_DIR}/${dir}" "${DEB_ROOT}/opt/inference-test-app/"
        elif [[ -d "${SCRIPT_DIR}/${dir}" ]]; then
            cp -r "${SCRIPT_DIR}/${dir}" "${DEB_ROOT}/opt/inference-test-app/"
        fi
    done

    # 复制摄像头权限修复脚本
    if [[ -f "${SCRIPT_DIR}/fix_camera_permissions.sh" ]]; then
        cp "${SCRIPT_DIR}/fix_camera_permissions.sh" "${DEB_ROOT}/opt/inference-test-app/"
        chmod +x "${DEB_ROOT}/opt/inference-test-app/fix_camera_permissions.sh"
        echo_info "摄像头权限修复脚本复制完成"
    fi

    echo_success "应用程序文件复制完成"
}

# 创建图标文件（如果不存在）
create_icon_if_missing() {
    local icon_dir="${DEB_ROOT}/opt/inference-test-app/icons"
    local icon_file="${icon_dir}/app-icon.png"
    
    if [[ ! -f "${icon_file}" ]]; then
        echo_info "创建默认图标..."
        mkdir -p "${icon_dir}"
        
        # 创建一个简单的 48x48 PNG 图标（使用 ImageMagick 如果可用）
        if command -v convert >/dev/null 2>&1; then
            convert -size 48x48 xc:lightblue -pointsize 20 -gravity center \
                    -annotate +0+0 "诊断" "${icon_file}" 2>/dev/null || {
                # 如果 ImageMagick 失败，创建一个占位符文件
                touch "${icon_file}"
            }
        else
            # 创建占位符文件
            touch "${icon_file}"
        fi
    fi
}

# 设置文件权限
set_file_permissions() {
    echo_info "设置文件权限..."
    
    # 设置目录权限
    find "${DEB_ROOT}/opt/inference-test-app" -type d -exec chmod 755 {} \;

    # 设置文件权限
    find "${DEB_ROOT}/opt/inference-test-app" -type f -exec chmod 644 {} \;

    # 设置可执行文件权限
    chmod +x "${DEB_ROOT}/opt/inference-test-app/${PROJECT_NAME}"
    if [[ -f "${DEB_ROOT}/opt/inference-test-app/run_inference_app.sh" ]]; then
        chmod +x "${DEB_ROOT}/opt/inference-test-app/run_inference_app.sh"
    fi

    # 设置图片保存目录权限（允许应用程序写入）
    chmod 755 "${DEB_ROOT}/opt/inference-test-app/saved_images"

    echo_success "文件权限设置完成"
}

# 构建 .deb 包
build_deb_package() {
    echo_info "构建 .deb 包..."
    
    cd "${DEB_BUILD_DIR}"
    
    # 使用 fakeroot 构建包
    fakeroot dpkg-deb --build "${PACKAGE_NAME}_${VERSION}_${ARCH}"
    
    if [[ $? -eq 0 ]]; then
        # 移动 .deb 文件到项目根目录
        mv "${PACKAGE_NAME}_${VERSION}_${ARCH}.deb" "${SCRIPT_DIR}/"
        echo_success ".deb 包构建成功: ${SCRIPT_DIR}/${PACKAGE_NAME}_${VERSION}_${ARCH}.deb"
    else
        echo_error ".deb 包构建失败"
        exit 1
    fi
    
    cd "${SCRIPT_DIR}"
}

# 验证包内容
verify_package() {
    echo_info "验证包内容..."
    
    local deb_file="${SCRIPT_DIR}/${PACKAGE_NAME}_${VERSION}_${ARCH}.deb"
    
    if [[ -f "${deb_file}" ]]; then
        echo_info "包信息:"
        dpkg-deb --info "${deb_file}"
        
        echo_info "包内容:"
        dpkg-deb --contents "${deb_file}"
        
        # 检查包大小
        local file_size=$(du -h "${deb_file}" | cut -f1)
        echo_info "包大小: ${file_size}"
        
        echo_success "包验证完成"
    else
        echo_error "包文件不存在: ${deb_file}"
        exit 1
    fi
}

# 显示安装说明
show_installation_instructions() {
    echo_info "=== 安装说明 ==="
    echo_success "🎉 .deb 包构建完成！"
    
    local deb_file="${PACKAGE_NAME}_${VERSION}_${ARCH}.deb"
    
    echo_info "包文件: ${deb_file}"
    echo_info ""
    echo_info "安装命令:"
    echo_info "  sudo dpkg -i ${deb_file}"
    echo_info "  sudo apt-get install -f  # 如果有依赖问题"
    echo_info ""
    echo_info "卸载命令:"
    echo_info "  sudo apt remove ${PACKAGE_NAME}        # 保留配置文件"
    echo_info "  sudo apt purge ${PACKAGE_NAME}         # 完全删除"
    echo_info ""
    echo_info "运行应用:"
    echo_info "  inference-test-app                          # 命令行运行"
    echo_info "  /opt/inference-test-app/run_inference_app.sh # 直接运行"
    echo_info ""
    echo_info "配置文件:"
    echo_info "  /etc/inference-test-app/inference.conf     # 主配置文件"
    echo_info "  /opt/inference-test-app/config/             # 应用配置目录"
}

# 主函数
main() {
    echo_info "开始构建 .deb 包..."
    
    # 执行构建步骤
    check_build_environment
    clean_previous_build
    create_package_structure
    copy_application_files
    create_icon_if_missing
    set_file_permissions
    build_deb_package
    verify_package
    show_installation_instructions
    
    echo_success "构建脚本执行完成！"
}

# 执行主函数
main "$@"
