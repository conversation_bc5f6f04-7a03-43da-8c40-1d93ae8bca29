#!/bin/bash
# 测试模拟内容清理效果

echo "=== 测试模拟内容清理效果 ==="

echo "1. 检查modelinference.cpp中的模拟内容:"
mock_count=$(grep -c "模拟\|mock\|Mock\|MOCK" modelinference.cpp || echo "0")
echo "  剩余模拟相关内容: $mock_count 处"

if [ "$mock_count" -eq 0 ]; then
    echo "  ✓ 所有模拟内容已清理"
else
    echo "  ! 仍有模拟内容残留"
    echo "  残留内容:"
    grep -n "模拟\|mock\|Mock\|MOCK" modelinference.cpp || echo "  无"
fi

echo ""
echo "2. 检查taskpage.cpp中的模拟内容:"
task_mock_count=$(grep -c "模拟\|mock\|Mock\|MOCK" taskpage.cpp || echo "0")
echo "  剩余模拟相关内容: $task_mock_count 处"

if [ "$task_mock_count" -eq 0 ]; then
    echo "  ✓ 所有模拟内容已清理"
else
    echo "  ! 仍有模拟内容残留"
fi

echo ""
echo "3. 检查配置文件中的模拟选项:"
if grep -q "enable_mock_result\|mock_result_delay\|auto_chinese_output" config/inference.conf.example; then
    echo "  ✗ 配置文件中仍有模拟选项"
    grep -n "enable_mock_result\|mock_result_delay\|auto_chinese_output" config/inference.conf.example
else
    echo "  ✓ 配置文件中的模拟选项已清理"
fi

echo ""
echo "4. 检查头文件中的模拟函数声明:"
if grep -q "createMockResult\|createRK3588MockResult\|parseRK3588Output" modelinference.h; then
    echo "  ✗ 头文件中仍有模拟函数声明"
    grep -n "createMockResult\|createRK3588MockResult\|parseRK3588Output" modelinference.h
else
    echo "  ✓ 头文件中的模拟函数声明已清理"
fi

echo ""
echo "5. 检查configmanager中的模拟相关方法:"
if grep -q "MockResult\|AutoChineseOutput" configmanager.h configmanager.cpp; then
    echo "  ✗ configmanager中仍有模拟相关方法"
    grep -n "MockResult\|AutoChineseOutput" configmanager.h configmanager.cpp
else
    echo "  ✓ configmanager中的模拟相关方法已清理"
fi

echo ""
echo "=== 清理效果总结 ==="
total_mock=$(grep -r "模拟\|mock\|Mock\|MOCK" *.cpp *.h config/ 2>/dev/null | wc -l || echo "0")
echo "项目中剩余模拟相关内容总数: $total_mock"

if [ "$total_mock" -eq 0 ]; then
    echo "✅ 所有模拟内容已成功清理"
else
    echo "⚠️  仍有部分模拟内容需要清理"
fi

echo ""
echo "=== 预期行为 ==="
echo "现在当推理程序不存在时，应用程序会："
echo "- 直接报错而不是显示模拟结果"
echo "- 显示具体的错误信息"
echo "- 不会泄露任何敏感的模拟数据"

echo ""
echo "=== 测试完成 ==="
