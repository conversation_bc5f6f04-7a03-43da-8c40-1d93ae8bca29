#!/bin/bash
# 测试提示词改进效果

echo "=== 测试提示词改进效果 ==="

echo "1. 检查提示词结构:"
if grep -q "请仔细观察图片并回答以下问题" taskpage.cpp; then
    echo "  ✓ 添加了观察指导语"
else
    echo "  ✗ 缺少观察指导语"
fi

if grep -q "请严格按照以下格式回答" taskpage.cpp; then
    echo "  ✓ 添加了格式要求强调"
else
    echo "  ✗ 缺少格式要求强调"
fi

if grep -q "必须包含分析和答案两个部分" taskpage.cpp; then
    echo "  ✓ 明确要求包含两个部分"
else
    echo "  ✗ 未明确要求两个部分"
fi

echo ""
echo "2. 检查分析部分要求:"
if grep -q "请详细分析图片内容，说明你的推理过程" taskpage.cpp; then
    echo "  ✓ 要求详细分析和推理过程"
else
    echo "  ✗ 缺少详细分析要求"
fi

echo ""
echo "3. 检查答案部分要求:"
if grep -q "如果是选择题请给出选项字母，如果是判断题请回答" taskpage.cpp; then
    echo "  ✓ 明确了不同题型的答案格式"
else
    echo "  ✗ 未明确不同题型的答案格式"
fi

echo ""
echo "=== 改进后的提示词结构 ==="
echo ""
echo "📋 新的提示词包含以下改进："
echo ""
echo "1. 🎯 明确指导："
echo "   - '请仔细观察图片并回答以下问题'"
echo "   - 引导模型注意观察细节"
echo ""
echo "2. 📝 格式强调："
echo "   - '请严格按照以下格式回答'"
echo "   - '必须包含分析和答案两个部分'"
echo "   - 强调格式的重要性"
echo ""
echo "3. 🔍 分析要求："
echo "   - '请详细分析图片内容，说明你的推理过程'"
echo "   - 要求模型展示思考过程"
echo ""
echo "4. ✅ 答案规范："
echo "   - 选择题：'请给出选项字母'"
echo "   - 判断题：'请回答正确或错误'"
echo "   - 明确不同题型的答案格式"
echo ""
echo "5. 📐 格式示例："
echo "   分析：（详细分析内容）"
echo "   答案：（具体答案）"
echo ""
echo "=== 预期效果 ==="
echo ""
echo "改进后的提示词应该能够："
echo "✅ 引导模型进行详细的图片分析"
echo "✅ 确保输出包含完整的推理过程"
echo "✅ 保证答案格式的一致性"
echo "✅ 提高模型回答的质量和可读性"

echo ""
echo "=== 测试建议 ==="
echo ""
echo "请重新编译并测试："
echo "1. ./build.sh"
echo "2. 运行应用程序"
echo "3. 选择图片和题目"
echo "4. 点击'开始推理'"
echo "5. 检查输出是否包含完整的'分析：'和'答案：'部分"

echo ""
echo "=== 测试完成 ==="
