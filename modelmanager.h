#ifndef MODELMANAGER_H
#define MODELMANAGER_H

#include <QObject>
#include <QProcess>
#include <QTimer>
#include <QString>
#include <QMutex>
#include <QWaitCondition>
#include "configmanager.h"

/**
 * @brief 模型管理器类
 * 负责管理RK3588 demo进程的生命周期，在程序启动时加载模型，
 * 保持进程运行状态，处理推理请求
 */
class ModelManager : public QObject
{
    Q_OBJECT

public:
    explicit ModelManager(QObject *parent = nullptr);
    ~ModelManager();

    // 初始化并启动模型
    bool initializeModel();
    
    // 停止模型
    void stopModel();
    
    // 检查模型是否已加载
    bool isModelLoaded() const { return m_isModelLoaded; }
    
    // 发送推理请求
    void sendInferenceRequest(const QString& imagePath, const QString& questionText);
    
    // 检查模型进程状态
    bool isProcessRunning() const;

signals:
    void modelLoaded();
    void modelLoadFailed(const QString& error);
    void inferenceCompleted(const QString& result);
    void inferenceError(const QString& error);
    void processStatusChanged(bool running);

private slots:
    void onProcessFinished(int exitCode, QProcess::ExitStatus exitStatus);
    void onProcessError(QProcess::ProcessError error);
    void onProcessReadyRead();
    void onInitTimeout();

private:
    void setupProcess();
    void cleanupProcess();
    QString buildModelLoadCommand() const;
    void parseProcessOutput(const QString& output);
    void attemptRestart(); // 尝试重启demo进程
    
    QProcess* m_demoProcess;
    QTimer* m_initTimer;
    bool m_isModelLoaded;
    bool m_isInitializing;
    QString m_accumulatedOutput;
    QMutex m_processMutex;

    // 重启相关
    int m_restartAttempts;
    QTimer* m_restartTimer;

    // 推理超时
    QTimer* m_inferenceTimer;
    
    // 配置参数
    QString m_demoPath;
    QString m_rknnModelPath;
    QString m_rkllmModelPath;
    int m_maxTokens;
    int m_contextLength;
    int m_coreNum;
    
    static const int INIT_TIMEOUT_MS = 60000; // 60秒初始化超时
    static const int MAX_RESTART_ATTEMPTS = 3; // 最大重启尝试次数
    static const int RESTART_DELAY_MS = 5000; // 重启延迟5秒
    static const int INFERENCE_TIMEOUT_MS = 30000; // 30秒推理超时
};

#endif // MODELMANAGER_H
