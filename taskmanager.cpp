#include "taskmanager.h"
#include <QDateTime>

const QString TaskManager::CONFIG_DIR_NAME = "config";
const QString TaskManager::CONFIG_FILE_NAME = "tasks.json";
const int TaskManager::MAX_TASKS = 10;

TaskManager::TaskManager(QObject *parent)
    : QObject(parent)
{
    initializeConfigDirectory();
    qDebug() << "TaskManager initialized";
}

TaskManager::~TaskManager()
{
    qDebug() << "TaskManager destroyed";
}

void TaskManager::initializeConfigDirectory()
{
    // 获取应用程序数据目录
    QString appDataDir = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    m_configDir = QDir(appDataDir).absoluteFilePath(CONFIG_DIR_NAME);
    m_configFile = QDir(m_configDir).absoluteFilePath(CONFIG_FILE_NAME);
    
    // 创建配置目录
    QDir dir;
    if (!dir.exists(m_configDir)) {
        if (dir.mkpath(m_configDir)) {
            qDebug() << "Created config directory:" << m_configDir;
        } else {
            qWarning() << "Failed to create config directory:" << m_configDir;
        }
    }
    
    qDebug() << "Config file path:" << m_configFile;
}

QString TaskManager::getConfigFilePath() const
{
    return m_configFile;
}

QList<TaskData> TaskManager::loadTasks()
{
    m_tasks.clear();
    
    // 尝试从文件加载
    if (QFile::exists(m_configFile)) {
        if (loadFromFile(m_configFile)) {
            qDebug() << "Loaded" << m_tasks.size() << "tasks from config file";
            emit tasksLoaded(m_tasks.size());
            return m_tasks;
        }
    }
    
    // 如果加载失败或文件不存在，创建默认任务
    qDebug() << "Creating default tasks";
    createDefaultTasks();
    
    // 保存默认任务到文件
    saveToFile(m_configFile);
    
    emit tasksLoaded(m_tasks.size());
    return m_tasks;
}

bool TaskManager::loadFromFile(const QString& filePath)
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly)) {
        qWarning() << "Cannot open config file for reading:" << filePath;
        emit configError(QString("无法打开配置文件: %1").arg(filePath));
        return false;
    }
    
    QByteArray data = file.readAll();
    file.close();
    
    QJsonParseError parseError;
    QJsonDocument doc = QJsonDocument::fromJson(data, &parseError);
    
    if (parseError.error != QJsonParseError::NoError) {
        qWarning() << "JSON parse error:" << parseError.errorString();
        emit configError(QString("配置文件格式错误: %1").arg(parseError.errorString()));
        return false;
    }
    
    if (!doc.isObject()) {
        qWarning() << "Config file root is not an object";
        emit configError("配置文件格式错误: 根节点不是对象");
        return false;
    }
    
    QJsonObject rootObj = doc.object();
    QJsonArray tasksArray = rootObj["tasks"].toArray();
    
    for (const auto& taskValue : tasksArray) {
        if (taskValue.isObject()) {
            TaskData task = TaskData::fromJson(taskValue.toObject());
            if (task.isValid()) {
                m_tasks.append(task);
            } else {
                qWarning() << "Invalid task data for task ID:" << task.taskId;
            }
        }
    }
    
    return true;
}

bool TaskManager::saveToFile(const QString& filePath) const
{
    QJsonObject rootObj;
    QJsonArray tasksArray;
    
    for (const TaskData& task : m_tasks) {
        tasksArray.append(task.toJson());
    }
    
    rootObj["tasks"] = tasksArray;
    rootObj["version"] = "1.0";
    rootObj["lastModified"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    
    QJsonDocument doc(rootObj);
    
    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly)) {
        qWarning() << "Cannot open config file for writing:" << filePath;
        return false;
    }
    
    qint64 bytesWritten = file.write(doc.toJson());
    file.close();
    
    if (bytesWritten == -1) {
        qWarning() << "Failed to write config file:" << filePath;
        return false;
    }
    
    qDebug() << "Saved" << m_tasks.size() << "tasks to config file";
    return true;
}

bool TaskManager::saveTasks(const QList<TaskData>& tasks)
{
    m_tasks = tasks;
    return saveToFile(m_configFile);
}

TaskData TaskManager::getTask(int taskId) const
{
    for (const TaskData& task : m_tasks) {
        if (task.taskId == taskId) {
            return task;
        }
    }
    return TaskData(); // 返回空任务
}

bool TaskManager::updateTask(const TaskData& task)
{
    for (int i = 0; i < m_tasks.size(); ++i) {
        if (m_tasks[i].taskId == task.taskId) {
            m_tasks[i] = task;
            bool saved = saveToFile(m_configFile);
            if (saved) {
                emit taskUpdated(task.taskId);
            }
            return saved;
        }
    }
    return false;
}

bool TaskManager::addTask(const TaskData& task)
{
    if (!validateTaskConfig(task)) {
        return false;
    }
    
    if (m_tasks.size() >= MAX_TASKS) {
        qWarning() << "Maximum number of tasks reached:" << MAX_TASKS;
        emit configError(QString("任务数量已达到最大限制: %1").arg(MAX_TASKS));
        return false;
    }
    
    // 检查任务ID是否已存在
    for (const TaskData& existingTask : m_tasks) {
        if (existingTask.taskId == task.taskId) {
            qWarning() << "Task ID already exists:" << task.taskId;
            emit configError(QString("任务ID已存在: %1").arg(task.taskId));
            return false;
        }
    }
    
    m_tasks.append(task);
    bool saved = saveToFile(m_configFile);
    if (saved) {
        emit taskAdded(task.taskId);
    }
    return saved;
}

bool TaskManager::removeTask(int taskId)
{
    for (int i = 0; i < m_tasks.size(); ++i) {
        if (m_tasks[i].taskId == taskId) {
            m_tasks.removeAt(i);
            bool saved = saveToFile(m_configFile);
            if (saved) {
                emit taskRemoved(taskId);
            }
            return saved;
        }
    }
    return false;
}

int TaskManager::getTaskCount() const
{
    return m_tasks.size();
}

bool TaskManager::validateTaskConfig(const TaskData& task) const
{
    if (!task.isValid()) {
        qWarning() << "Task validation failed: basic validation";
        return false;
    }
    
    // 检查模型文件路径
    if (!task.modelPath.isEmpty() && !QFile::exists(task.modelPath)) {
        qWarning() << "Model file does not exist:" << task.modelPath;
        return false;
    }
    
    return true;
}

void TaskManager::createDefaultTasks()
{
    m_tasks.clear();

    // 注意：默认任务列表已不再使用
    // 应用程序直接从questions.json文件加载题目
    // 题目数量完全由questions.json文件决定

    qDebug() << "Default tasks creation skipped - using questions.json instead";
}

TaskData TaskManager::createDefaultTask(int taskId, const QString& taskName,
                                       const QString& itemType) const
{
    // 注意：此函数已不再使用
    // 应用程序直接从questions.json文件加载题目
    Q_UNUSED(taskId)
    Q_UNUSED(taskName)
    Q_UNUSED(itemType)

    TaskData task;
    task.taskId = 0;
    task.taskName = "未使用";
    task.isActive = false;
    
    // 设置模型路径（需要根据实际部署调整）
    task.modelPath = QString("models/%1_model.rknn").arg(itemType.toLower());
    task.configPath = QString("config/%1_config.json").arg(itemType.toLower());
    
    return task;
}

void TaskManager::resetAllTasks()
{
    createDefaultTasks();
    saveToFile(m_configFile);
    emit tasksLoaded(m_tasks.size());
    qDebug() << "All tasks reset to default";
}
