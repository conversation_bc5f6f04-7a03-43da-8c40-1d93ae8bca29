#!/bin/bash
# 测试项目重命名效果

echo "=== 测试项目重命名效果 ==="

echo "1. 检查项目文件重命名:"
if [ -f "InferenceApp.pro" ]; then
    echo "  ✓ InferenceApp.pro 已创建"
else
    echo "  ✗ InferenceApp.pro 不存在"
fi

if [ -f "InferenceApp.qrc" ]; then
    echo "  ✓ InferenceApp.qrc 已创建"
else
    echo "  ✗ InferenceApp.qrc 不存在"
fi

echo ""
echo "2. 检查项目文件内容:"
if grep -q "TARGET   = InferenceApp" InferenceApp.pro 2>/dev/null; then
    echo "  ✓ .pro文件中TARGET已更新"
else
    echo "  ✗ .pro文件中TARGET未更新"
fi

if grep -q "InferenceApp.qrc" InferenceApp.pro 2>/dev/null; then
    echo "  ✓ .pro文件中资源文件引用已更新"
else
    echo "  ✗ .pro文件中资源文件引用未更新"
fi

echo ""
echo "3. 检查构建脚本:"
if grep -q 'PROJECT_NAME="InferenceApp"' build.sh 2>/dev/null; then
    echo "  ✓ build.sh中项目名已更新"
else
    echo "  ✗ build.sh中项目名未更新"
fi

if grep -q 'PROJECT_NAME="InferenceApp"' create_deb_package.sh 2>/dev/null; then
    echo "  ✓ create_deb_package.sh中项目名已更新"
else
    echo "  ✗ create_deb_package.sh中项目名未更新"
fi

echo ""
echo "4. 检查启动脚本:"
if grep -q "./InferenceApp" build/run_inference_app.sh 2>/dev/null; then
    echo "  ✓ 启动脚本中可执行文件名已更新"
else
    echo "  ✗ 启动脚本中可执行文件名未更新"
fi

echo ""
echo "5. 检查.deb包配置:"
if grep -q "InferenceApp" debian/DEBIAN/postinst 2>/dev/null; then
    echo "  ✓ .deb包安装脚本已更新"
else
    echo "  ✗ .deb包安装脚本未更新"
fi

echo ""
echo "6. 检查文档:"
if grep -q "InferenceApp" README.md 2>/dev/null; then
    echo "  ✓ README.md已更新"
else
    echo "  ✗ README.md未更新"
fi

if grep -q "InferenceApp" DEB_PACKAGE_README.md 2>/dev/null; then
    echo "  ✓ DEB_PACKAGE_README.md已更新"
else
    echo "  ✗ DEB_PACKAGE_README.md未更新"
fi

echo ""
echo "7. 检查是否还有DiagnosisApp残留:"
diagnosis_count=$(grep -r "DiagnosisApp" *.sh *.md *.pro *.qrc debian/ build/ 2>/dev/null | wc -l || echo "0")
echo "  DiagnosisApp残留引用: $diagnosis_count 处"

if [ "$diagnosis_count" -eq 0 ]; then
    echo "  ✓ 所有DiagnosisApp引用已清理"
else
    echo "  ! 仍有DiagnosisApp引用残留"
    echo "  残留位置:"
    grep -rn "DiagnosisApp" *.sh *.md *.pro *.qrc debian/ build/ 2>/dev/null | head -5
fi

echo ""
echo "=== TaskManager机制说明 ==="
echo "关于您询问的TaskManager默认任务列表："
echo ""
echo "✅ 重要发现：TaskManager实际上没有被使用！"
echo ""
echo "📋 实际的题目加载机制："
echo "  - 应用程序直接从questions.json文件加载题目"
echo "  - MainWindow::loadTasks()直接创建TaskPage"
echo "  - 完全不依赖TaskManager的默认任务列表"
echo ""
echo "🔄 questions.json变动的影响："
echo "  - ✅ 题目个数可以任意变动"
echo "  - ✅ 不会影响任何功能"
echo "  - ✅ 应用程序会自动适应题目数量"
echo "  - ✅ 导航按钮会根据实际题目数量调整"
echo ""
echo "🗑️  TaskManager状态："
echo "  - TaskManager中的默认任务列表已被标记为'未使用'"
echo "  - 这些代码是历史遗留，可以安全忽略"
echo "  - 所有功能都基于questions.json文件"

echo ""
echo "=== 测试完成 ==="
