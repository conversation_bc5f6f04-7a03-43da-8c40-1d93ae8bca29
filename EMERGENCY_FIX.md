# 紧急修复 - prerm脚本问题

## 问题描述

prerm脚本在安装和卸载时都会被中断，导致：
- 重新安装时在"准备解压"阶段中断
- 卸载时进程停止阶段中断
- 包状态变为异常

## 根本原因

prerm脚本中的进程检查和停止逻辑过于复杂，可能在以下环节出现问题：
1. `pgrep`命令执行时
2. 进程ID处理时
3. 条件判断时
4. 进程停止时

## 立即解决方案

### 1. 清理当前状态

在ARM64设备上执行：

```bash
# 强制清理当前包状态
sudo dpkg --remove --force-remove-reinstreq diagnosis-app 2>/dev/null || true
sudo dpkg --purge --force-remove-reinstreq diagnosis-app 2>/dev/null || true

# 手动停止所有相关进程
sudo pkill -9 -f "DiagnosisApp" 2>/dev/null || true
sudo pkill -9 -f "diagnosis-app" 2>/dev/null || true

# 清理残留文件
sudo rm -rf /opt/diagnosis-app 2>/dev/null || true
sudo rm -f /usr/local/bin/diagnosis-app 2>/dev/null || true
sudo rm -f /usr/share/applications/diagnosis-app.desktop 2>/dev/null || true

# 清理dpkg信息
sudo rm -f /var/lib/dpkg/info/diagnosis-app.* 2>/dev/null || true

# 重新配置dpkg
sudo dpkg --configure -a
```

### 2. 重新构建包（版本1.0.3）

```bash
cd test
./build_deb.sh --clean
```

### 3. 测试新版本

```bash
# 安装
sudo dpkg -i diagnosis-app_1.0.3_arm64.deb
sudo apt-get install -f

# 测试卸载
sudo apt remove --purge diagnosis-app
```

## 新prerm脚本特点

版本1.0.3的prerm脚本采用极简设计：

1. **移除所有复杂逻辑**：不再检查进程是否存在
2. **直接执行停止命令**：使用`|| true`确保不会失败
3. **最小化输出**：减少可能导致中断的操作
4. **快速执行**：整个脚本在2秒内完成

```bash
#!/bin/bash
# 极简版本 - 最大化可靠性

log_info() {
    echo "[INFO] $1" >&2
}

log_info "准备卸载部署模型推理测试系统..."
log_info "停止应用程序进程..."

# 直接尝试停止，不检查是否存在
pkill -f "DiagnosisApp" >/dev/null 2>&1 || true
pkill -f "diagnosis-app" >/dev/null 2>&1 || true
sleep 1
pkill -9 -f "DiagnosisApp" >/dev/null 2>&1 || true
pkill -9 -f "diagnosis-app" >/dev/null 2>&1 || true

log_info "进程停止完成"
exit 0
```

## 如果问题仍然存在

### 方案A：完全禁用prerm脚本

```bash
# 安装后立即替换prerm脚本
sudo dpkg -i diagnosis-app_1.0.3_arm64.deb

# 创建空的prerm脚本
sudo tee /var/lib/dpkg/info/diagnosis-app.prerm > /dev/null << 'EOF'
#!/bin/bash
echo "[INFO] 跳过进程停止"
exit 0
EOF

sudo chmod +x /var/lib/dpkg/info/diagnosis-app.prerm
```

### 方案B：使用dpkg的忽略选项

```bash
# 安装时忽略脚本错误
sudo dpkg -i --force-maintainer-script-errors diagnosis-app_1.0.3_arm64.deb

# 卸载时忽略脚本错误
sudo dpkg -r --force-maintainer-script-errors diagnosis-app
```

### 方案C：手动安装

```bash
# 解压包到临时目录
mkdir -p /tmp/diagnosis-app-manual
dpkg-deb --extract diagnosis-app_1.0.3_arm64.deb /tmp/diagnosis-app-manual

# 手动复制文件
sudo cp -r /tmp/diagnosis-app-manual/* /

# 手动注册包（不执行脚本）
sudo dpkg --unpack diagnosis-app_1.0.3_arm64.deb
sudo dpkg --configure diagnosis-app
```

## 调试信息收集

如果问题仍然存在，请收集以下信息：

```bash
# 系统信息
uname -a
lsb_release -a

# 进程信息
ps aux | grep -E "DiagnosisApp|diagnosis-app"

# dpkg状态
dpkg -l | grep diagnosis-app
cat /var/lib/dpkg/status | grep -A 10 "Package: diagnosis-app"

# 系统日志
journalctl -u dpkg --since "1 hour ago"

# 详细安装过程
sudo dpkg -i --debug=1 diagnosis-app_1.0.3_arm64.deb
```

## 预防措施

1. **安装前检查**：确保没有同名进程运行
2. **使用systemd服务**：考虑将应用程序作为systemd服务管理
3. **简化包结构**：减少复杂的安装/卸载逻辑

## 联系支持

如果所有方案都无法解决问题，这可能是系统特定的问题。请提供：

1. 完整的错误日志
2. 系统信息
3. 之前的安装历史
4. 任何自定义的系统配置

这将帮助进一步分析问题的根本原因。
