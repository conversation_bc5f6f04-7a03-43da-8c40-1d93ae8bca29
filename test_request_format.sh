#!/bin/bash
# 测试请求格式

echo "测试正确的请求格式..."

DEMO_PATH="/userdata/install/demo_Linux_aarch64/demo"
RKNN_MODEL="/userdata/models/qwen2_vl_2b_vision_rk3588.rknn"
RKLLM_MODEL="/userdata/models/Qwen2-2B-vl-Instruct.rkllm"
TEST_IMAGE="/opt/inference-test-app/saved_images/captured_20250723_194029.jpg"

# 使用新的参数：max_tokens=256, context_length=1024
echo "启动demo程序（新参数）..."
echo "命令: $DEMO_PATH $RKNN_MODEL $RKLLM_MODEL 256 1024 4"

# 测试长文本（使用\n表示换行，避免真实换行导致的问题）
LONG_TEXT="请仔细观察图片并回答以下问题。\n\n题目：图中是什么物品？只需返回正确的选项编号。\nA. 物品A\nB. 物品B\nC. 物品C \nD. 物品D\n\n<image>\n\n请严格按照以下格式回答，必须包含分析和答案两个部分：\n\n分析：（请详细分析图片内容，说明你的推理过程）\n\n答案：（请给出最终答案，如果是选择题请给出选项字母，如果是判断题请回答'正确'或'错误'）"

echo "测试长文本请求..."
echo "文本长度: ${#LONG_TEXT} 字符"

# 使用expect来自动化交互
expect << 'EOF'
spawn /userdata/install/demo_Linux_aarch64/demo /userdata/models/qwen2_vl_2b_vision_rk3588.rknn /userdata/models/Qwen2-2B-vl-Instruct.rkllm 256 1024 4
expect "user:" {
    # 发送完整的请求（图片路径 + 空格 + 文本内容）
    send "/opt/inference-test-app/saved_images/captured_20250723_194029.jpg 请仔细观察图片并回答以下问题。\\n\\n题目：图中是什么物品？只需返回正确的选项编号。\\nA. 物品A\\nB. 物品B\\nC. 物品C \\nD. 物品D\\n\\n<image>\\n\\n请严格按照以下格式回答，必须包含分析和答案两个部分：\\n\\n分析：（请详细分析图片内容，说明你的推理过程）\\n\\n答案：（请给出最终答案，如果是选择题请给出选项字母，如果是判断题请回答'正确'或'错误'）\r"
    expect "robot:" {
        puts "收到robot响应"
        expect "user:" {
            send "exit\r"
        }
    }
}
expect eof
EOF

echo "测试完成"
