from PIL import Image
import sys

def resize_image(input_path, output_path):
    try:
        # 打开图片
        img = Image.open(input_path)
        
        # 进行缩放操作，保持宽高比例并调整到 392x392
        img_resized = img.resize((392, 392))
        
        # 保存处理后的图片
        img_resized.save(output_path)
        print(f"图片已成功保存到 {output_path}")
    
    except Exception as e:
        print(f"处理图片时发生错误: {e}")

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("用法: python resize_image.py <输入图片路径> <输出图片路径>")
    else:
        input_path = sys.argv[1]
        output_path = sys.argv[2]
        resize_image(input_path, output_path)
