#!/bin/bash
# 一键构建和打包脚本

set -e

# 颜色输出
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

echo_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
echo_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }

echo_info "=== 模型推理测试系统一键构建和打包 ==="

# 1. 编译项目
echo_info "步骤 1: 编译项目..."
if [[ -f "./build.sh" ]]; then
    chmod +x ./build.sh
    ./build.sh
else
    echo "错误: 找不到 build.sh 脚本"
    exit 1
fi

# 2. 创建 .deb 包
echo_info "步骤 2: 创建 .deb 包..."
if [[ -f "./create_deb_package.sh" ]]; then
    chmod +x ./create_deb_package.sh
    ./create_deb_package.sh
else
    echo "错误: 找不到 create_deb_package.sh 脚本"
    exit 1
fi

echo_success "=== 构建和打包完成！==="
echo_info "生成的 .deb 包可以在当前目录找到"
