linaro@linaro-alip:/userdata/test$ sudo ./build/run_inference_app.sh
启动部署模型推理测试系统...
QStandardPaths: XDG_RUNTIME_DIR not set, defaulting to '/tmp/runtime-root'
=== 模型推理测试系统启动 ===
Qt版本: 5.15.2
应用程序路径: "/userdata/test/build"
清空图片保存目录: "/opt/inference-test-app/saved_images"
已删除 1 个图片文件
Config file path: "/root/.local/share/Medical AI Solutions/部署模型推理测试系统/config/tasks.json"
TaskManager initialized
配置文件已加载: "/etc/inference-test-app/inference.conf"
Worker thread started
ModelInference initialized with timeout: 300 seconds
设备 "/dev/video0" 匹配分析:
  RGB Camera名称: 是(+2)
  硬件版本0x00000001: 是(+1)
  KP26BLD系列: 否(+0)
  总分: 3
找到完全匹配的目标RGB Camera: "/dev/video0"
TaskPage created
Found questions file at: "/userdata/test/build/config/questions.json"
Adding 4 choices
Added choice: "A. 物品A"
Added choice: "B. 物品B"
Added choice: "C. 物品C "
Added choice: "D. 物品D"
Task page loaded with questions from: "/userdata/test/build/config/questions.json"
MainWindow initialized successfully
开始程序初始化...
ModelManager initialized with config:
  Demo路径: "/userdata/install/demo_Linux_aarch64/demo"
  RKNN模型: "/userdata/models/qwen2_vl_2b_vision_rk3588.rknn"
  RKLLM模型: "/userdata/models/Qwen2-2B-vl-Instruct.rkllm"
TaskPage: ModelManager已设置并连接信号
开始初始化模型...
ModelManager::initializeModel() 开始
启动模型加载进程: "/userdata/install/demo_Linux_aarch64/demo" ("/userdata/models/qwen2_vl_2b_vision_rk3588.rknn", "/userdata/models/Qwen2-2B-vl-Instruct.rkllm", "256", "1024", "4")
Demo进程已启动，等待模型加载完成...
部署模型推理测试系统启动成功
Qt版本: 5.15.2
应用程序路径: "/userdata/test/build"
模型加载完成
模型状态变更: 运行中
模型加载完成
模型加载完成，显示主窗口
libGL error: failed to create dri screen
libGL error: failed to load driver: rockchip
libGL error: failed to create dri screen
libGL error: failed to load driver: rockchip
[调试] 使用摄像头设备: "/dev/video0"
[调试] 拍照命令: "ffmpeg -f v4l2 -video_size 640x480 -framerate 30 -pixel_format yuyv422 -i /dev/video0 -vf 'transpose=2,transpose=2' -pix_fmt yuvj420p -vframes 1 -y /opt/inference-test-app/saved_images/captured_20250727_005914.jpg"
[调试] 拍照成功: "/opt/inference-test-app/saved_images/captured_20250727_005914.jpg"
Saved state for question 0 - hasImage: true - answer: ""
Image loaded: "/opt/inference-test-app/saved_images/captured_20250727_005914.jpg"
=== 开始RK3588推理调试 ===
当前题目索引: 0
题目总数: 2
当前图片路径: "/opt/inference-test-app/saved_images/captured_20250727_005914.jpg"
是否有图片: true
图片文件是否存在: true
开始推理: "/opt/inference-test-app/saved_images/captured_20250727_005914.jpg"
当前题目内容: "图中是什么物品？只需返回正确的选项编号。\nA. 物品A\nB. 物品B\nC. 物品C \nD. 物品D"
当前题目答案: "B"
构建的题目文本长度: 178
构建的题目文本内容:
"请仔细观察图片并回答以下问题。\n\n题目：图中是什么物品？只需返回正确的选项编号。\nA. 物品A\nB. 物品B\nC. 物品C \nD. 物品D\n\n<image>\n\n请严格按照以下格式回答，必须包含分析和答案两个部分：\n\n分析：（请详细分析图片内容，说明你的推理过程）\n\n答案：（请给出最终答案，如果是选择题请给出选项字母，如果是判断题请回答'正确'或'错误'）"
========================
开始推理请求: "/opt/inference-test-app/saved_images/captured_20250727_005914.jpg"
=== ModelManager推理请求调试 ===
图片路径: "/opt/inference-test-app/saved_images/captured_20250727_005914.jpg"
图片文件是否存在: true
题目文本长度: 178
题目文本内容:
"请仔细观察图片并回答以下问题。\n\n题目：图中是什么物品？只需返回正确的选项编号。\nA. 物品A\nB. 物品B\nC. 物品C \nD. 物品D\n\n<image>\n\n请严格按照以下格式回答，必须包含分析和答案两个部分：\n\n分析：（请详细分析图片内容，说明你的推理过程）\n\n答案：（请给出最终答案，如果是选择题请给出选项字母，如果是判断题请回答'正确'或'错误'）"
处理后文本内容:
"请仔细观察图片并回答以下问题。\\n\\n题目：图中是什么物品？只需返回正确的选项编号。\\nA. 物品A\\nB. 物品B\\nC. 物品C \\nD. 物品D\\n\\n<image>\\n\\n请严格按照以下格式回答，必须包含分析和答案两个部分：\\n\\n分析：（请详细分析图片内容，说明你的推理过程）\\n\\n答案：（请给出最终答案，如果是选择题请给出选项字母，如果是判断题请回答'正确'或'错误'）"
完整请求内容:
"/opt/inference-test-app/saved_images/captured_20250727_005914.jpg 请仔细观察图片并回答以下问题。\\n\\n题目：图中是什么物品？只需返回正确的选项编号。\\nA. 物品A\\nB. 物品B\\nC. 物品C \\nD. 物品D\\n\\n<image>\\n\\n请严格按照以下格式回答，必须包含分析和答案两个部分：\\n\\n分析：（请详细分析图片内容，说明你的推理过程）\\n\\n答案：（请给出最终答案，如果是选择题请给出选项字母，如果是判断题请回答'正确'或'错误'）\n"
=========================
推理请求已发送，等待响应...
推理完成
Saved state for question 0 - hasImage: true - answer: ""
=== 切换到下一题调试 ===
切换前题目索引: 0
切换前图片路径: "/opt/inference-test-app/saved_images/captured_20250727_005914.jpg"
切换前是否有图片: true
Saved state for question 0 - hasImage: true - answer: ""
切换后题目索引: 1
Adding judgment options
=== 恢复题目状态调试 ===
要恢复的题目索引: 1
是否有保存的状态: false
Cleared state for current question
恢复状态后图片路径: ""
恢复状态后是否有图片: false
========================
Moved to next question: 1
Saved state for question 1 - hasImage: true - answer: ""
Image loaded: "/userdata/infer_test.jpg"
=== 开始RK3588推理调试 ===
当前题目索引: 1
题目总数: 2
当前图片路径: "/userdata/infer_test.jpg"
是否有图片: true
图片文件是否存在: true
开始推理: "/userdata/infer_test.jpg"
当前题目内容: "请回答以下判断题，你只需要输出“错误”或是“正确”\n题目如下：图中物品具有特定属性。"
当前题目答案: "错误"
构建的题目文本长度: 171
构建的题目文本内容:
"请仔细观察图片并回答以下问题。\n\n题目：请回答以下判断题，你只需要输出“错误”或是“正确”\n题目如下：图中物品具有特定属性。\n\n<image>\n\n请严格按照以下格式回答，必须包含分析和答案两个部分：\n\n分析：（请详细分析图片内容，说明你的推理过程）\n\n答案：（请给出最终答案，如果是选择题请给出选项字母，如果是判断题请回答'正确'或'错误'）"
========================
开始推理请求: "/userdata/infer_test.jpg"
=== ModelManager推理请求调试 ===
图片路径: "/userdata/infer_test.jpg"
图片文件是否存在: true
题目文本长度: 171
题目文本内容:
"请仔细观察图片并回答以下问题。\n\n题目：请回答以下判断题，你只需要输出“错误”或是“正确”\n题目如下：图中物品具有特定属性。\n\n<image>\n\n请严格按照以下格式回答，必须包含分析和答案两个部分：\n\n分析：（请详细分析图片内容，说明你的推理过程）\n\n答案：（请给出最终答案，如果是选择题请给出选项字母，如果是判断题请回答'正确'或'错误'）"
处理后文本内容:
"请仔细观察图片并回答以下问题。\\n\\n题目：请回答以下判断题，你只需要输出“错误”或是“正确”\\n题目如下：图中物品具有特定属性。\\n\\n<image>\\n\\n请严格按照以下格式回答，必须包含分析和答案两个部分：\\n\\n分析：（请详细分析图片内容，说明你的推理过程）\\n\\n答案：（请给出最终答案，如果是选择题请给出选项字母，如果是判断题请回答'正确'或'错误'）"
完整请求内容:
"/userdata/infer_test.jpg 请仔细观察图片并回答以下问题。\\n\\n题目：请回答以下判断题，你只需要输出“错误”或是“正确”\\n题目如下：图中物品具有特定属性。\\n\\n<image>\\n\\n请严格按照以下格式回答，必须包含分析和答案两个部分：\\n\\n分析：（请详细分析图片内容，说明你的推理过程）\\n\\n答案：（请给出最终答案，如果是选择题请给出选项字母，如果是判断题请回答'正确'或'错误'）\n"
=========================
推理请求已发送，等待响应...
推理完成
Saved state for question 1 - hasImage: true - answer: ""
=== 开始RK3588推理调试 ===
当前题目索引: 1
题目总数: 2
当前图片路径: "/userdata/infer_test.jpg"
是否有图片: true
图片文件是否存在: true
开始推理: "/userdata/infer_test.jpg"
当前题目内容: "请回答以下判断题，你只需要输出“错误”或是“正确”\n题目如下：图中物品具有特定属性。"
当前题目答案: "错误"
构建的题目文本长度: 171
构建的题目文本内容:
"请仔细观察图片并回答以下问题。\n\n题目：请回答以下判断题，你只需要输出“错误”或是“正确”\n题目如下：图中物品具有特定属性。\n\n<image>\n\n请严格按照以下格式回答，必须包含分析和答案两个部分：\n\n分析：（请详细分析图片内容，说明你的推理过程）\n\n答案：（请给出最终答案，如果是选择题请给出选项字母，如果是判断题请回答'正确'或'错误'）"
========================
开始推理请求: "/userdata/infer_test.jpg"
=== ModelManager推理请求调试 ===
图片路径: "/userdata/infer_test.jpg"
图片文件是否存在: true
题目文本长度: 171
题目文本内容:
"请仔细观察图片并回答以下问题。\n\n题目：请回答以下判断题，你只需要输出“错误”或是“正确”\n题目如下：图中物品具有特定属性。\n\n<image>\n\n请严格按照以下格式回答，必须包含分析和答案两个部分：\n\n分析：（请详细分析图片内容，说明你的推理过程）\n\n答案：（请给出最终答案，如果是选择题请给出选项字母，如果是判断题请回答'正确'或'错误'）"
处理后文本内容:
"请仔细观察图片并回答以下问题。\\n\\n题目：请回答以下判断题，你只需要输出“错误”或是“正确”\\n题目如下：图中物品具有特定属性。\\n\\n<image>\\n\\n请严格按照以下格式回答，必须包含分析和答案两个部分：\\n\\n分析：（请详细分析图片内容，说明你的推理过程）\\n\\n答案：（请给出最终答案，如果是选择题请给出选项字母，如果是判断题请回答'正确'或'错误'）"
完整请求内容:
"/userdata/infer_test.jpg 请仔细观察图片并回答以下问题。\\n\\n题目：请回答以下判断题，你只需要输出“错误”或是“正确”\\n题目如下：图中物品具有特定属性。\\n\\n<image>\\n\\n请严格按照以下格式回答，必须包含分析和答案两个部分：\\n\\n分析：（请详细分析图片内容，说明你的推理过程）\\n\\n答案：（请给出最终答案，如果是选择题请给出选项字母，如果是判断题请回答'正确'或'错误'）\n"
=========================
推理请求已发送，等待响应...
推理完成
Saved state for question 1 - hasImage: true - answer: "
