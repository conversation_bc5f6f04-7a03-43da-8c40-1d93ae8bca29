#!/bin/bash
# 简化的格式测试

echo "测试简化的请求格式..."

DEMO_PATH="/userdata/install/demo_Linux_aarch64/demo"
RKNN_MODEL="/userdata/models/qwen2_vl_2b_vision_rk3588.rknn"
RKLLM_MODEL="/userdata/models/Qwen2-2B-vl-Instruct.rkllm"
TEST_IMAGE="/opt/inference-test-app/saved_images/captured_20250723_194029.jpg"

echo "启动demo程序（新参数：256, 1024, 4）..."

# 使用expect来测试单行格式
expect << 'EOF'
spawn /userdata/install/demo_Linux_aarch64/demo /userdata/models/qwen2_vl_2b_vision_rk3588.rknn /userdata/models/Qwen2-2B-vl-Instruct.rkllm 256 1024 4
expect "user:" {
    # 测试单行格式（使用\\n代替真实换行）
    send "/opt/inference-test-app/saved_images/captured_20250723_194029.jpg 请仔细观察图片并回答以下问题。\\n\\n题目：图中是什么物品？\\nA. 物品A\\nB. 物品B\\nC. 物品C\\nD. 物品D\\n\\n<image>\\n\\n请回答选项字母。\r"
    expect "robot:" {
        puts "收到robot响应，测试成功"
        expect "user:" {
            send "exit\r"
        }
    }
}
expect eof
EOF

echo "测试完成"
