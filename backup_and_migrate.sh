#!/bin/bash
# 部署模型推理测试系统 - 备份和迁移工具
# 展示自包含目录结构的优势

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
echo_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
echo_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
echo_error() { echo -e "${RED}[ERROR]${NC} $1"; }

APP_DIR="/opt/diagnosis-app"
BACKUP_DIR="/tmp/diagnosis-app-backups"

show_usage() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  backup              创建应用程序备份"
    echo "  restore <备份文件>   从备份恢复应用程序"
    echo "  migrate <目标主机>   迁移应用程序到其他设备"
    echo "  list                列出所有备份文件"
    echo "  help                显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 backup"
    echo "  $0 restore /tmp/diagnosis-app-backups/backup-20231204-143022.tar.gz"
    echo "  $0 migrate user@192.168.1.100"
    echo ""
}

create_backup() {
    echo_info "=== 创建应用程序备份 ==="
    
    if [[ ! -d "$APP_DIR" ]]; then
        echo_error "应用程序目录不存在: $APP_DIR"
        echo_info "请先安装应用程序"
        exit 1
    fi
    
    # 创建备份目录
    mkdir -p "$BACKUP_DIR"
    
    # 生成备份文件名
    TIMESTAMP=$(date +%Y%m%d-%H%M%S)
    BACKUP_FILE="${BACKUP_DIR}/backup-${TIMESTAMP}.tar.gz"
    
    echo_info "备份目录: $APP_DIR"
    echo_info "备份文件: $BACKUP_FILE"
    
    # 创建备份
    echo_info "正在创建备份..."
    tar -czf "$BACKUP_FILE" -C "$(dirname "$APP_DIR")" "$(basename "$APP_DIR")"
    
    # 显示备份信息
    BACKUP_SIZE=$(du -h "$BACKUP_FILE" | cut -f1)
    echo_success "✅ 备份创建完成！"
    echo_info "备份文件: $BACKUP_FILE"
    echo_info "备份大小: $BACKUP_SIZE"
    
    # 显示备份内容摘要
    echo_info ""
    echo_info "备份内容摘要:"
    tar -tzf "$BACKUP_FILE" | head -20
    if [[ $(tar -tzf "$BACKUP_FILE" | wc -l) -gt 20 ]]; then
        echo_info "... 还有 $(($(tar -tzf "$BACKUP_FILE" | wc -l) - 20)) 个文件"
    fi
}

restore_backup() {
    local backup_file="$1"
    
    echo_info "=== 从备份恢复应用程序 ==="
    
    if [[ ! -f "$backup_file" ]]; then
        echo_error "备份文件不存在: $backup_file"
        exit 1
    fi
    
    echo_info "备份文件: $backup_file"
    echo_info "恢复目录: $APP_DIR"
    
    # 确认操作
    if [[ -d "$APP_DIR" ]]; then
        echo_warning "目标目录已存在，恢复将覆盖现有文件"
        read -p "是否继续？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            echo_info "恢复已取消"
            exit 0
        fi
    fi
    
    # 创建父目录
    mkdir -p "$(dirname "$APP_DIR")"
    
    # 恢复备份
    echo_info "正在恢复备份..."
    tar -xzf "$backup_file" -C "$(dirname "$APP_DIR")"
    
    # 设置权限
    if command -v chown >/dev/null 2>&1; then
        echo_info "设置文件权限..."
        chown -R diagnosis:diagnosis "$APP_DIR" 2>/dev/null || true
        chmod +x "$APP_DIR/DiagnosisApp" 2>/dev/null || true
        chmod +x "$APP_DIR/run_diagnosis_app.sh" 2>/dev/null || true
    fi
    
    echo_success "✅ 恢复完成！"
    echo_info "应用程序已恢复到: $APP_DIR"
}

migrate_to_remote() {
    local target_host="$1"
    
    echo_info "=== 迁移应用程序到远程设备 ==="
    
    if [[ ! -d "$APP_DIR" ]]; then
        echo_error "应用程序目录不存在: $APP_DIR"
        exit 1
    fi
    
    echo_info "源设备: $(hostname)"
    echo_info "目标设备: $target_host"
    echo_info "应用目录: $APP_DIR"
    
    # 创建临时备份
    TEMP_BACKUP="/tmp/diagnosis-app-migrate-$(date +%Y%m%d-%H%M%S).tar.gz"
    echo_info "创建临时备份: $TEMP_BACKUP"
    tar -czf "$TEMP_BACKUP" -C "$(dirname "$APP_DIR")" "$(basename "$APP_DIR")"
    
    # 传输到目标设备
    echo_info "传输文件到目标设备..."
    scp "$TEMP_BACKUP" "$target_host:/tmp/"
    
    # 在目标设备上恢复
    echo_info "在目标设备上恢复应用程序..."
    ssh "$target_host" << EOF
        # 创建目标目录
        sudo mkdir -p "$(dirname "$APP_DIR")"
        
        # 解压应用程序
        sudo tar -xzf "/tmp/$(basename "$TEMP_BACKUP")" -C "$(dirname "$APP_DIR")"
        
        # 设置权限
        sudo chown -R diagnosis:diagnosis "$APP_DIR" 2>/dev/null || true
        sudo chmod +x "$APP_DIR/DiagnosisApp" 2>/dev/null || true
        sudo chmod +x "$APP_DIR/run_diagnosis_app.sh" 2>/dev/null || true
        
        # 清理临时文件
        rm -f "/tmp/$(basename "$TEMP_BACKUP")"
        
        echo "✅ 应用程序迁移完成！"
        echo "应用目录: $APP_DIR"
        echo "启动命令: $APP_DIR/run_diagnosis_app.sh"
EOF
    
    # 清理本地临时文件
    rm -f "$TEMP_BACKUP"
    
    echo_success "✅ 迁移完成！"
    echo_info "应用程序已成功迁移到 $target_host"
}

list_backups() {
    echo_info "=== 备份文件列表 ==="
    
    if [[ ! -d "$BACKUP_DIR" ]] || [[ -z "$(ls -A "$BACKUP_DIR" 2>/dev/null)" ]]; then
        echo_warning "没有找到备份文件"
        echo_info "备份目录: $BACKUP_DIR"
        return
    fi
    
    echo_info "备份目录: $BACKUP_DIR"
    echo_info ""
    
    for backup in "$BACKUP_DIR"/*.tar.gz; do
        if [[ -f "$backup" ]]; then
            local size=$(du -h "$backup" | cut -f1)
            local date=$(stat -c %y "$backup" | cut -d' ' -f1,2 | cut -d'.' -f1)
            echo_success "📦 $(basename "$backup")"
            echo_info "   大小: $size"
            echo_info "   日期: $date"
            echo_info "   路径: $backup"
            echo_info ""
        fi
    done
}

# 主程序
case "${1:-help}" in
    "backup")
        create_backup
        ;;
    "restore")
        if [[ -z "$2" ]]; then
            echo_error "请指定备份文件"
            echo_info "用法: $0 restore <备份文件>"
            exit 1
        fi
        restore_backup "$2"
        ;;
    "migrate")
        if [[ -z "$2" ]]; then
            echo_error "请指定目标主机"
            echo_info "用法: $0 migrate <目标主机>"
            exit 1
        fi
        migrate_to_remote "$2"
        ;;
    "list")
        list_backups
        ;;
    "help"|*)
        show_usage
        ;;
esac

exit 0
