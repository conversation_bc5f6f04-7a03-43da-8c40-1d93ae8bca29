#include "configmanager.h"
#include <QStandardPaths>
#include <QDir>
#include <QFile>
#include <QCoreApplication>

ConfigManager* ConfigManager::m_instance = nullptr;

ConfigManager* ConfigManager::instance()
{
    if (!m_instance) {
        m_instance = new ConfigManager();
    }
    return m_instance;
}

ConfigManager::ConfigManager(QObject *parent)
    : QObject(parent), m_settings(nullptr)
{
    loadConfig();
}

ConfigManager::~ConfigManager()
{
    delete m_settings;
}

void ConfigManager::loadConfig()
{
    QString configPath = getConfigFilePath();
    
    if (m_settings) {
        delete m_settings;
    }
    
    m_settings = new QSettings(configPath, QSettings::IniFormat);
    m_settings->setIniCodec("UTF-8");
    
    // 清空缓存
    m_configCache.clear();
    
    qDebug() << "配置文件已加载:" << configPath;
    
    // 验证配置文件是否可读
    if (m_settings->status() != QSettings::NoError) {
        emit configError("配置文件读取失败: " + configPath);
    }
}

QString ConfigManager::getConfigFilePath() const
{
    // 优先使用系统配置目录
    QString systemConfigPath = "/etc/inference-test-app/inference.conf";
    if (QFile::exists(systemConfigPath)) {
        return systemConfigPath;
    }
    
    // 其次使用应用程序目录下的配置文件
    QString appConfigPath = QCoreApplication::applicationDirPath() + "/config/inference.conf";
    if (QFile::exists(appConfigPath)) {
        return appConfigPath;
    }
    
    // 最后使用示例配置文件
    QString exampleConfigPath = QCoreApplication::applicationDirPath() + "/config/inference.conf.example";
    if (QFile::exists(exampleConfigPath)) {
        qWarning() << "使用示例配置文件:" << exampleConfigPath;
        return exampleConfigPath;
    }
    
    qWarning() << "未找到配置文件，使用默认设置";
    return QString();
}

// 通用配置
int ConfigManager::getTimeoutSeconds() const
{
    return m_settings ? m_settings->value("General/timeout_seconds", 300).toInt() : 300;
}

QString ConfigManager::getTempDirectory() const
{
    return m_settings ? m_settings->value("General/temp_directory", "/tmp/inference-test-app").toString() : "/tmp/inference-test-app";
}

QString ConfigManager::getLogLevel() const
{
    return m_settings ? m_settings->value("General/log_level", "INFO").toString() : "INFO";
}

QString ConfigManager::getLogFile() const
{
    return m_settings ? m_settings->value("General/log_file", "/var/log/inference-test-app/app.log").toString() : "/var/log/inference-test-app/app.log";
}

// RK3588推理配置
QString ConfigManager::getRK3588DemoPath() const
{
    return m_settings ? m_settings->value("RK3588_Inference/demo_path", "/userdata/install/demo_Linux_aarch64/demo").toString() : "/userdata/install/demo_Linux_aarch64/demo";
}

QString ConfigManager::getRK3588RknnModelPath() const
{
    return m_settings ? m_settings->value("RK3588_Inference/rknn_model_path", "/userdata/models/qwen2_vl_2b_vision_rk3588.rknn").toString() : "/userdata/models/qwen2_vl_2b_vision_rk3588.rknn";
}

QString ConfigManager::getRK3588RkllmModelPath() const
{
    return m_settings ? m_settings->value("RK3588_Inference/rkllm_model_path", "/userdata/models/Qwen2-2B-vl-Instruct.rkllm").toString() : "/userdata/models/Qwen2-2B-vl-Instruct.rkllm";
}

int ConfigManager::getRK3588MaxTokens() const
{
    return m_settings ? m_settings->value("RK3588_Inference/max_tokens", 128).toInt() : 128;
}

int ConfigManager::getRK3588ContextLength() const
{
    return m_settings ? m_settings->value("RK3588_Inference/context_length", 512).toInt() : 512;
}

int ConfigManager::getRK3588CoreNum() const
{
    return m_settings ? m_settings->value("RK3588_Inference/core_num", 4).toInt() : 4;
}

// 自动中文输出方法已删除

// 标准推理配置
QString ConfigManager::getStandardExecutablePath() const
{
    return m_settings ? m_settings->value("Standard_Inference/executable_path", "./inference_engine").toString() : "./inference_engine";
}

// 模拟结果相关方法已删除

// UI设置
int ConfigManager::getProgressUpdateInterval() const
{
    return m_settings ? m_settings->value("UI_Settings/progress_update_interval", 100).toInt() : 100;
}

bool ConfigManager::getShowDetailedErrors() const
{
    return m_settings ? m_settings->value("UI_Settings/show_detailed_errors", true).toBool() : true;
}

QString ConfigManager::getDefaultInputPlaceholder() const
{
    return m_settings ? m_settings->value("UI_Settings/default_input_placeholder", "请输入相关信息...").toString() : "请输入相关信息...";
}

// 相机设置
QString ConfigManager::getCameraDetectionMode() const
{
    return m_settings ? m_settings->value("Camera_Settings/camera_detection_mode", "auto").toString() : "auto";
}

QStringList ConfigManager::getManualCameraDevices() const
{
    QString devices = m_settings ? m_settings->value("Camera_Settings/manual_camera_devices", "/dev/video0,/dev/video1,/dev/video2").toString() : "/dev/video0,/dev/video1,/dev/video2";
    return devices.split(",", Qt::SkipEmptyParts);
}

int ConfigManager::getCameraInitTimeout() const
{
    return m_settings ? m_settings->value("Camera_Settings/camera_init_timeout", 10).toInt() : 10;
}

int ConfigManager::getImageSaveQuality() const
{
    return m_settings ? m_settings->value("Camera_Settings/image_save_quality", 85).toInt() : 85;
}

// 模型路径
QString ConfigManager::getModelPath(const QString& medicineType) const
{
    QString key = QString("Model_Paths/%1_model").arg(medicineType.toLower());
    QString defaultPath = QString("models/%1_model.rknn").arg(medicineType.toLower());
    return m_settings ? m_settings->value(key, defaultPath).toString() : defaultPath;
}

QString ConfigManager::getModelConfig(const QString& medicineType) const
{
    QString key = QString("Model_Configs/%1_config").arg(medicineType.toLower());
    QString defaultPath = QString("config/%1_config.json").arg(medicineType.toLower());
    return m_settings ? m_settings->value(key, defaultPath).toString() : defaultPath;
}

void ConfigManager::reloadConfig()
{
    loadConfig();
    emit configChanged();
}
