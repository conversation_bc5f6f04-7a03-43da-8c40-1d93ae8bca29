#!/bin/bash
# 模型推理测试系统启动脚本

# 设置工作目录
cd "$(dirname "${BASH_SOURCE[0]}")"

# 设置Qt环境变量
export LD_LIBRARY_PATH="/usr/lib/aarch64-linux-gnu/qt5/lib:${LD_LIBRARY_PATH}"
export QT_PLUGIN_PATH="/usr/lib/aarch64-linux-gnu/qt5/plugins"
export LD_LIBRARY_PATH="/userdata/install/demo_Linux_aarch64/lib:${LD_LIBRARY_PATH}"

# 设置字体环境变量
export QT_QPA_FONTDIR="/usr/share/fonts"
export FONTCONFIG_PATH="/etc/fonts"

# 启动应用程序
echo "启动模型推理测试系统..."
./InferenceApp
