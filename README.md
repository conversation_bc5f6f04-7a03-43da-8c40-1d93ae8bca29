# 部署模型推理测试系统

## 📋 系统说明

这是一个通用的模型推理测试应用程序，支持多模态模型（RKNN + RKLLM）在RK3588平台上的部署和测试。

## 🏗️ 安装

```bash
# 安装.deb包
sudo dpkg -i inference-test-app_1.0.0_arm64.deb

# 如果有依赖问题
sudo apt-get install -f
```

## ⚙️ 配置

### 1. 模型文件配置

编辑 `/opt/inference-test-app/config/default_tasks.json`：

```json
{
    "version": "1.0",
    "models": {
        "rknn_model": "models/vision_model.rknn",
        "rkllm_model": "models/llm_model.rkllm",
        "max_tokens": 128,
        "context_length": 512
    },
    "questions_file": "config/questions.json"
}
```

### 2. 放置模型文件

将您的模型文件放置到以下位置：

```
/opt/inference-test-app/
├── models/
│   ├── vision_model.rknn      # RKNN视觉模型
│   └── llm_model.rkllm        # RKLLM语言模型
└── config/
    ├── default_tasks.json     # 配置文件
    └── questions.json         # 问题数据文件
```

### 3. 问题数据格式

`questions.json` 文件格式：

```json
[
  {
    "Q": "问题文本",
    "A": "正确答案",
    "image": ["/path/to/image.jpg"]
  }
]
```

## 🚀 运行

```bash
# 命令行启动
inference-test-app

# 或者直接运行
/opt/inference-test-app/DiagnosisApp
```

## 🔧 模型调用方式

应用程序内部使用以下方式调用模型：

```bash
./demo <image_path> <rknn_model> <rkllm_model> <max_tokens> <context_length>
```

实际调用示例：
```bash
./demo /tmp/input.jpg \
       /opt/inference-test-app/models/vision_model.rknn \
       /opt/inference-test-app/models/llm_model.rkllm \
       128 512
```

## 📁 目录结构

```
/opt/inference-test-app/         # 应用程序主目录
├── InferenceApp                 # 主程序
├── run_inference_app.sh         # 启动脚本
├── saved_images/                # 摄像头拍摄图片保存目录
├── models/                      # 模型文件目录（需要用户提供）
│   ├── vision_model.rknn        # RKNN模型
│   └── llm_model.rkllm          # RKLLM模型
├── config/                      # 配置文件目录
│   ├── default_tasks.json       # 主配置文件
│   └── questions.json           # 问题数据（需要用户提供）
├── data/                        # 用户数据目录
├── logs/                        # 日志文件目录
├── images/                      # 图片文件目录
├── temp/                        # 临时文件目录
└── docs/                        # 文档目录
```

## ⚠️ 注意事项

1. **模型文件**：需要用户自行提供RKNN和RKLLM模型文件
2. **问题数据**：需要用户自行提供questions.json文件
3. **路径配置**：可以通过修改default_tasks.json来调整模型文件路径
4. **权限要求**：确保应用程序对模型文件和配置文件有读取权限

## 🗑️ 卸载

```bash
# 完全卸载（包括数据）
sudo apt remove --purge inference-test-app

# 或使用清理脚本
sudo ./cleanup_system.sh
```

## 📝 支持的平台

- **目标平台**: ARM64 Debian系统
- **硬件要求**: RK3588芯片组
- **依赖**: Qt5.12+, 相关RKNN/RKLLM运行时库

## 🔍 故障排除

### 常见问题

1. **检查日志**: `tail -f /opt/inference-test-app/logs/diagnosis_app.log`
2. **检查权限**: `ls -la /opt/inference-test-app/`
3. **验证模型**: 确保模型文件存在且可读
4. **检查配置**: 验证JSON配置文件格式正确

### 摄像头权限问题

如果遇到摄像头拍照失败，请运行权限修复脚本：
```bash
/opt/inference-test-app/fix_camera_permissions.sh
```

或手动修复：
```bash
# 将用户添加到video组
sudo usermod -a -G video $USER

# 重新登录或激活组权限
newgrp video

# 临时设置摄像头权限
sudo chmod 666 /dev/video*
```
