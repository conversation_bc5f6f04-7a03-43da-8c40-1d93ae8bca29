# 卸载问题快速修复指南

## 当前问题

你的测试显示卸载过程在prerm脚本执行时被中断，包状态变为 `rF`（卸载失败）。

## 立即解决方案

### 1. 重新构建包（版本1.0.2）

```bash
cd test
./build_deb.sh --clean
```

这将生成 `diagnosis-app_1.0.2_arm64.deb`，包含改进的prerm脚本。

### 2. 清理当前失败状态

在ARM64设备上执行：

```bash
# 使用调试脚本清理
./debug_uninstall.sh cleanup

# 或者手动清理
sudo dpkg --remove --force-remove-reinstreq diagnosis-app
sudo dpkg --purge --force-remove-reinstreq diagnosis-app
sudo rm -rf /opt/diagnosis-app
sudo rm -f /usr/local/bin/diagnosis-app
sudo rm -f /usr/share/applications/diagnosis-app.desktop
sudo dpkg --configure -a
```

### 3. 测试新版本

```bash
# 安装新版本
sudo dpkg -i diagnosis-app_1.0.2_arm64.deb
sudo apt-get install -f

# 测试卸载
sudo apt remove --purge diagnosis-app
```

## 主要改进

### prerm脚本改进

1. **移除了 `set -e`**：避免脚本因为进程不存在而中断
2. **简化了进程停止逻辑**：使用更可靠的方法
3. **添加了错误容忍**：即使部分操作失败也继续执行
4. **确保总是返回成功**：防止dpkg认为卸载失败

### 新的调试工具

- `debug_uninstall.sh`：全面的卸载问题诊断和修复工具
- `test_install_uninstall.sh`：自动化的安装卸载测试

## 如果问题仍然存在

### 方法1：使用调试脚本

```bash
./debug_uninstall.sh
# 选择选项7进行完整诊断
# 选择选项6进行强制清理
```

### 方法2：手动调试

```bash
# 查看详细的卸载过程
sudo dpkg -r --debug=1 diagnosis-app

# 手动测试prerm脚本
sudo bash -x /var/lib/dpkg/info/diagnosis-app.prerm remove

# 查看系统日志
journalctl -u dpkg -f
```

### 方法3：跳过prerm脚本

如果prerm脚本仍然有问题，可以临时禁用它：

```bash
# 备份原脚本
sudo cp /var/lib/dpkg/info/diagnosis-app.prerm /tmp/diagnosis-app.prerm.bak

# 创建简单的prerm脚本
sudo tee /var/lib/dpkg/info/diagnosis-app.prerm > /dev/null << 'EOF'
#!/bin/bash
echo "[INFO] 简化的prerm脚本"
pkill -f "DiagnosisApp" 2>/dev/null || true
pkill -f "diagnosis-app" 2>/dev/null || true
sleep 2
pkill -9 -f "DiagnosisApp" 2>/dev/null || true
pkill -9 -f "diagnosis-app" 2>/dev/null || true
echo "[INFO] prerm完成"
exit 0
EOF

sudo chmod +x /var/lib/dpkg/info/diagnosis-app.prerm

# 然后尝试卸载
sudo apt remove --purge diagnosis-app
```

## 根本原因分析

从你的日志看，问题出现在：

1. **多个进程同时存在**：发现了5个diagnosis-app进程
2. **进程停止时中断**：在尝试停止这些进程时脚本被终止
3. **可能的信号处理问题**：某些进程可能对TERM信号响应异常

新的prerm脚本通过以下方式解决：

- 使用更简单的进程停止逻辑
- 添加错误容忍机制
- 确保脚本总是成功退出

## 预防措施

为了避免将来出现类似问题：

1. **在安装前检查**：确保没有同名进程在运行
2. **使用测试脚本**：定期运行 `test_install_uninstall.sh`
3. **监控进程**：安装后检查是否有异常的多进程情况

## 联系支持

如果以上方法都无法解决问题，请提供：

1. `debug_uninstall.sh diagnose` 的完整输出
2. `/var/log/dpkg.log` 的相关部分
3. `journalctl -u dpkg` 的输出
4. 系统信息：`uname -a` 和 `lsb_release -a`

这将帮助进一步诊断问题。
