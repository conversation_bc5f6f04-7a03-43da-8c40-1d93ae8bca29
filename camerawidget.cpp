#include "camerawidget.h"
#include <QDateTime>
#include <QProcess>
#include <QFile>

const QString CameraWidget::IMAGES_DIR_NAME = "captured_images";

CameraWidget::CameraWidget(QWidget *parent)
    : QWidget(parent)
    , m_mainLayout(nullptr)
    , m_controlLayout(nullptr)
    , m_viewfinder(nullptr)
    , m_cameraComboBox(nullptr)
    , m_startStopButton(nullptr)
    , m_captureButton(nullptr)
    , m_statusLabel(nullptr)
    , m_camera(nullptr)
    , m_imageCapture(nullptr)
    , m_isCameraActive(false)
    , m_currentCameraIndex(-1)
{
    setupUI();
    setupCamera();
    connectSignals();
    
    // 创建图片保存目录
    m_imagesSaveDir = "/opt/inference-test-app/saved_images";
    QDir().mkpath(m_imagesSaveDir);
    
    qDebug() << "CameraWidget initialized";
    qDebug() << "Images save directory:" << m_imagesSaveDir;
}

CameraWidget::~CameraWidget()
{
    qDebug() << "[调试] CameraWidget析构开始";

    // 停止相机
    stopCamera();

    // 清理所有进程
    QList<QProcess*> processes = findChildren<QProcess*>();
    for (QProcess* process : processes) {
        if (process && process->state() != QProcess::NotRunning) {
            qDebug() << "[调试] 终止相机进程:" << process;
            process->kill();
            if (!process->waitForFinished(2000)) {
                process->terminate();
            }
        }
    }

    // 清理所有定时器
    QList<QTimer*> timers = findChildren<QTimer*>();
    for (QTimer* timer : timers) {
        if (timer) {
            timer->stop();
            timer->deleteLater();
        }
    }

    qDebug() << "[调试] CameraWidget析构完成";
}

void CameraWidget::setupUI()
{
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setContentsMargins(5, 5, 5, 5);
    m_mainLayout->setSpacing(10);
    
    // 相机预览区域
    m_viewfinder = new QCameraViewfinder(this);
    m_viewfinder->setFixedSize(VIEWFINDER_WIDTH, VIEWFINDER_HEIGHT);
    m_mainLayout->addWidget(m_viewfinder);
    
    // 控制区域
    m_controlLayout = new QHBoxLayout();
    
    // 相机选择
    m_cameraComboBox = new QComboBox(this);
    m_cameraComboBox->setMinimumWidth(150);
    m_controlLayout->addWidget(m_cameraComboBox);
    
    // 开始/停止按钮
    m_startStopButton = new QPushButton("启动相机", this);
    m_startStopButton->setMinimumHeight(35);
    m_controlLayout->addWidget(m_startStopButton);
    
    // 拍照按钮
    m_captureButton = new QPushButton("拍照", this);
    m_captureButton->setMinimumHeight(35);
    m_captureButton->setEnabled(false);
    m_controlLayout->addWidget(m_captureButton);
    
    m_controlLayout->addStretch();
    m_mainLayout->addLayout(m_controlLayout);
    
    // 状态标签
    m_statusLabel = new QLabel("相机未启动", this);
    m_statusLabel->setAlignment(Qt::AlignCenter);
    m_statusLabel->setStyleSheet("color: #888888; font-size: 11px;");
    m_mainLayout->addWidget(m_statusLabel);
    
    // 更新相机列表
    updateCameraList();
}

void CameraWidget::setupCamera()
{
    // 简化相机设置，仅用于UI初始化
    // 实际拍照使用ffmpeg命令，避免设备占用冲突
    qDebug() << "[调试] 初始化相机组件（仅UI，不占用设备）";

    // 检查相机设备是否存在（不占用）
    if (QFile::exists("/dev/video0")) {
        m_statusLabel->setText("相机设备可用");
        qDebug() << "[调试] 检测到 /dev/video0 设备";
    } else {
        m_statusLabel->setText("未检测到相机设备");
        m_startStopButton->setEnabled(false);
        qWarning() << "[调试] 未找到 /dev/video0 设备";
        return;
    }

    // 不再创建QCamera对象，避免设备占用
    // m_camera 和 m_imageCapture 保持为 nullptr
    qDebug() << "[调试] 相机组件初始化完成（使用ffmpeg拍照模式）";
}

void CameraWidget::connectSignals()
{
    // 按钮信号
    connect(m_startStopButton, &QPushButton::clicked, this, &CameraWidget::onStartStopClicked);
    connect(m_captureButton, &QPushButton::clicked, this, &CameraWidget::onCaptureClicked);

    // 移除相机选择功能，因为直接使用/dev/video0
    m_cameraComboBox->setVisible(false);

    // 不再需要Qt相机信号连接，使用ffmpeg模式
}

void CameraWidget::updateCameraList()
{
    m_cameraComboBox->clear();
    
    for (int i = 0; i < m_availableCameras.size(); ++i) {
        const QCameraInfo &cameraInfo = m_availableCameras[i];
        QString displayName = QString("%1 (%2)")
                             .arg(cameraInfo.description())
                             .arg(cameraInfo.deviceName());
        m_cameraComboBox->addItem(displayName);
        
        if (cameraInfo == QCameraInfo::defaultCamera()) {
            m_cameraComboBox->setCurrentIndex(i);
            m_currentCameraIndex = i;
        }
    }
    
    if (m_availableCameras.isEmpty()) {
        m_cameraComboBox->addItem("无可用相机");
        m_cameraComboBox->setEnabled(false);
    }
}

void CameraWidget::updateUI()
{
    if (m_isCameraActive) {
        m_startStopButton->setText("停止相机");
        m_captureButton->setEnabled(true);
        m_cameraComboBox->setEnabled(false);
        m_statusLabel->setText("相机运行中");
    } else {
        m_startStopButton->setText("启动相机");
        m_captureButton->setEnabled(false);
        m_cameraComboBox->setEnabled(true);
        m_statusLabel->setText("相机已停止");
    }
}

QString CameraWidget::createImageSavePath() const
{
    QString timestamp = QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss");
    QString fileName = QString("captured_%1.jpg").arg(timestamp);
    return QDir(m_imagesSaveDir).absoluteFilePath(fileName);
}

void CameraWidget::showCameraError(const QString& errorMessage)
{
    m_statusLabel->setText("错误: " + errorMessage);
    m_statusLabel->setStyleSheet("color: #ff4444; font-size: 11px;");
    emit cameraError(errorMessage);
}

void CameraWidget::startCamera()
{
    // 使用ffmpeg模式，不需要启动Qt相机
    if (m_isCameraActive) {
        qDebug() << "[调试] 相机已处于活动状态";
        return;
    }

    // 检查设备是否可用
    if (!QFile::exists("/dev/video0")) {
        showCameraError("相机设备不可用");
        return;
    }

    m_isCameraActive = true;
    m_statusLabel->setText("相机运行中（ffmpeg模式）");
    m_statusLabel->setStyleSheet("color: #00aa00; font-size: 11px;");
    updateUI();
    emit cameraStateChanged(true);
    qDebug() << "[调试] 相机启动（ffmpeg模式）";
}

void CameraWidget::stopCamera()
{
    if (!m_isCameraActive) {
        qDebug() << "[调试] 相机已停止";
        return;
    }

    m_isCameraActive = false;
    m_statusLabel->setText("相机已停止");
    m_statusLabel->setStyleSheet("color: #888888; font-size: 11px;");
    updateUI();
    emit cameraStateChanged(false);
    qDebug() << "[调试] 相机停止";
}

void CameraWidget::captureImage()
{
    qDebug() << "[调试] captureImage called, m_isCameraActive:" << m_isCameraActive
             << ", m_camera:" << (m_camera ? "not null" : "null")
             << ", camera state:" << (m_camera ? m_camera->state() : -1);

    QString imagePath = createImageSavePath();
    qDebug() << "[调试] 准备保存图片到:" << imagePath;

    // 确保保存目录存在
    QDir saveDir = QFileInfo(imagePath).absoluteDir();
    if (!saveDir.exists()) {
        saveDir.mkpath(".");
        qDebug() << "[调试] 创建保存目录:" << saveDir.absolutePath();
    }

    m_statusLabel->setText("正在拍照...");

    // 使用更简单的拍照方法，避免段错误
    QProcess *process = new QProcess();

    // 使用ffmpeg进行拍照，更稳定
    QString cmd = QString("ffmpeg -f v4l2 -video_size 640x480 -framerate 30 -pixel_format yuyv422 -i /dev/video0 -vf 'transpose=2,transpose=2' -pix_fmt yuvj420p -vframes 1 -y %1").arg(imagePath);
    qDebug() << "[调试] 执行拍照命令:" << cmd;

    // 设置进程超时
    QTimer *timeoutTimer = new QTimer();
    timeoutTimer->setSingleShot(true);
    timeoutTimer->setInterval(8000); // 8秒超时

    connect(timeoutTimer, &QTimer::timeout, [this, process, timeoutTimer]() {
        qDebug() << "[调试] 拍照超时，终止进程";
        if (process->state() != QProcess::NotRunning) {
            process->kill();
        }
        showCameraError("拍照超时");
        timeoutTimer->deleteLater();
    });

    connect(process, QOverload<int, QProcess::ExitStatus>::of(&QProcess::finished),
            [this, process, imagePath, timeoutTimer](int exitCode, QProcess::ExitStatus exitStatus) {
                timeoutTimer->stop();
                timeoutTimer->deleteLater();

                qDebug() << "[调试] 拍照进程结束, exitCode:" << exitCode << "exitStatus:" << exitStatus;

                // 检查文件是否存在且大小合理
                QFileInfo fileInfo(imagePath);
                if (exitCode == 0 && fileInfo.exists() && fileInfo.size() > 1024) {
                    qDebug() << "[调试] 拍照成功:" << imagePath << "文件大小:" << fileInfo.size();
                    emit imageCaptured(imagePath);
                    m_statusLabel->setText("图片已保存");
                } else {
                    qDebug() << "[调试] 拍照失败 - exitCode:" << exitCode
                             << "文件存在:" << fileInfo.exists()
                             << "文件大小:" << fileInfo.size();

                    // 如果ffmpeg失败，尝试使用fswebcam
                    QString backupCmd = QString("fswebcam -r 640x480 --no-banner %1").arg(imagePath);
                    qDebug() << "[调试] 尝试备用拍照命令:" << backupCmd;

                    int backupResult = QProcess::execute("/bin/sh", QStringList() << "-c" << backupCmd);
                    QFileInfo backupFileInfo(imagePath);

                    if (backupResult == 0 && backupFileInfo.exists() && backupFileInfo.size() > 1024) {
                        qDebug() << "[调试] 备用拍照成功:" << imagePath;
                        emit imageCaptured(imagePath);
                        m_statusLabel->setText("图片已保存");
                    } else {
                        showCameraError("拍照失败，请检查相机连接");
                    }
                }

                process->deleteLater();
            });

    // 启动拍照进程
    timeoutTimer->start();
    process->start("/bin/sh", QStringList() << "-c" << cmd);

    if (!process->waitForStarted(2000)) {
        qDebug() << "[调试] 拍照进程启动失败";
        timeoutTimer->stop();
        timeoutTimer->deleteLater();
        showCameraError("拍照进程启动失败");
        process->deleteLater();
    }
}

bool CameraWidget::isCameraAvailable() const
{
    return QFile::exists("/dev/video0");
}

QStringList CameraWidget::getAvailableCameras() const
{
    QStringList cameraNames;
    for (const QCameraInfo &info : m_availableCameras) {
        cameraNames.append(info.description());
    }
    return cameraNames;
}

void CameraWidget::setCurrentCamera(int index)
{
    if (index < 0 || index >= m_availableCameras.size()) {
        qWarning() << "Invalid camera index:" << index;
        return;
    }
    
    if (index == m_currentCameraIndex) {
        return;
    }
    
    // 停止当前相机
    bool wasActive = m_isCameraActive;
    if (wasActive) {
        stopCamera();
    }
    
    // 切换到新相机
    if (m_camera) {
        delete m_camera;
        m_camera = nullptr;
    }
    
    if (m_imageCapture) {
        delete m_imageCapture;
        m_imageCapture = nullptr;
    }
    
    // 创建新相机
    const QCameraInfo &cameraInfo = m_availableCameras[index];
    m_camera = new QCamera(cameraInfo, this);
    m_imageCapture = new QCameraImageCapture(m_camera, this);
    
    m_camera->setViewfinder(m_viewfinder);
    connectSignals();
    
    m_currentCameraIndex = index;
    m_cameraComboBox->setCurrentIndex(index);
    
    // 如果之前是活动状态，重新启动
    if (wasActive) {
        startCamera();
    }
    
    qDebug() << "Switched to camera:" << cameraInfo.description();
}

void CameraWidget::onCameraStateChanged(QCamera::State state)
{
    qDebug() << "[调试] onCameraStateChanged, state:" << state;
    switch (state) {
    case QCamera::ActiveState:
        m_isCameraActive = true;
        m_statusLabel->setText("相机运行中");
        m_statusLabel->setStyleSheet("color: #00aa00; font-size: 11px;");
        emit cameraStateChanged(true);
        break;
    case QCamera::LoadedState:
        m_statusLabel->setText("相机已加载");
        break;
    case QCamera::UnloadedState:
        m_isCameraActive = false;
        m_statusLabel->setText("相机已停止");
        m_statusLabel->setStyleSheet("color: #888888; font-size: 11px;");
        emit cameraStateChanged(false);
        break;
    }
    
    updateUI();
    qDebug() << "Camera state changed to:" << state;
}

void CameraWidget::onCameraError(QCamera::Error error)
{
    QString errorString;
    switch (error) {
    case QCamera::NoError:
        return;
    case QCamera::CameraError:
        errorString = "相机设备错误";
        break;
    case QCamera::InvalidRequestError:
        errorString = "无效的相机请求";
        break;
    case QCamera::ServiceMissingError:
        errorString = "相机服务不可用";
        break;
    case QCamera::NotSupportedFeatureError:
        errorString = "不支持的相机功能";
        break;
    default:
        errorString = "未知相机错误";
        break;
    }
    
    showCameraError(errorString);
    qWarning() << "Camera error:" << error << errorString;
}

void CameraWidget::onImageCaptured(int id, const QImage& image)
{
    Q_UNUSED(id)
    Q_UNUSED(image)
    
    m_statusLabel->setText("图片已捕获，正在保存...");
    qDebug() << "Image captured, ID:" << id;
}

void CameraWidget::onImageSaved(int id, const QString& fileName)
{
    Q_UNUSED(id)
    
    m_lastCapturedImagePath = fileName;
    m_statusLabel->setText("图片已保存");
    m_statusLabel->setStyleSheet("color: #00aa00; font-size: 11px;");
    
    emit imageCaptured(fileName);
    
    qDebug() << "Image saved:" << fileName;
    
    // 3秒后恢复正常状态显示
    QTimer::singleShot(3000, [this]() {
        if (m_isCameraActive) {
            m_statusLabel->setText("相机运行中");
            m_statusLabel->setStyleSheet("color: #00aa00; font-size: 11px;");
        }
    });
}

void CameraWidget::onCaptureError(int id, QCameraImageCapture::Error error, const QString& errorString)
{
    Q_UNUSED(id)
    Q_UNUSED(error)
    
    showCameraError("拍照失败: " + errorString);
    qWarning() << "Capture error:" << error << errorString;
}

void CameraWidget::onStartStopClicked()
{
    if (m_isCameraActive) {
        stopCamera();
    } else {
        startCamera();
    }
}

void CameraWidget::onCaptureClicked()
{
    qDebug() << "[调试] 拍照按钮被点击";
    captureImage();
}

void CameraWidget::onCameraSelectionChanged(int index)
{
    if (index >= 0 && index < m_availableCameras.size()) {
        setCurrentCamera(index);
    }
}
