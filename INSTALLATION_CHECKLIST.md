# 模型推理测试系统 - 安装检查清单

## ✅ 安装前检查

### 系统要求确认
- [ ] 系统架构为 arm64 (运行 `uname -m` 确认显示 `aarch64`)
- [ ] 操作系统为 Debian 10+ 或 Ubuntu 18.04+
- [ ] 有管理员权限 (可以使用 `sudo`)
- [ ] 至少有 1GB 可用存储空间
- [ ] 摄像头设备已连接

### 依赖检查
```bash
# 检查Qt5是否安装
dpkg -l | grep qtbase5-dev
dpkg -l | grep qtmultimedia5-dev

# 如果没有安装，运行：
sudo apt update
sudo apt install -y qtbase5-dev qtmultimedia5-dev libqt5multimedia5-plugins
```

---

## 📦 安装步骤检查

### 1. 文件准备
- [ ] 已获取 `inference-test-app_1.0.0_arm64.deb` 文件
- [ ] 文件已传输到目标设备
- [ ] 文件完整性正常 (文件大小合理，可以正常读取)

### 2. 执行安装
```bash
# 安装命令
sudo dpkg -i inference-test-app_1.0.0_arm64.deb

# 修复依赖（如果需要）
sudo apt-get install -f
```

### 3. 安装验证
- [ ] 安装过程无错误信息
- [ ] 包已正确安装：`dpkg -l | grep inference-test-app`
- [ ] 程序文件存在：`ls -la /opt/inference-test-app/InferenceApp`
- [ ] 配置文件存在：`ls -la /etc/inference-test-app/inference.conf`

---

## 🔧 配置检查

### 权限设置
```bash
# 添加用户到video组
sudo usermod -a -G video $USER

# 验证用户组
groups | grep video

# 重新登录或运行
newgrp video
```

### 摄像头检查
```bash
# 检查摄像头设备
ls /dev/video*

# 检查设备权限
ls -la /dev/video*

# 测试摄像头（可选）
sudo /opt/inference-test-app/fix_camera_permissions.sh
```

### 目录权限检查
- [ ] `/opt/inference-test-app/` 目录存在且可访问
- [ ] `/opt/inference-test-app/saved_images/` 目录存在
- [ ] `/etc/inference-test-app/` 目录存在
- [ ] `/var/log/inference-test-app/` 目录存在

---

## 🚀 功能测试

### 启动测试
```bash
# 方法1：命令行启动
inference-test-app

# 方法2：直接运行
/opt/inference-test-app/run_inference_app.sh
```

### 基本功能测试
- [ ] 程序能正常启动
- [ ] 界面显示正常
- [ ] 摄像头能正常工作
- [ ] 能够拍摄图片
- [ ] 图片能保存到 `/opt/inference-test-app/saved_images/`

### 推理功能测试（如果已配置模型）
- [ ] 能输入问题文本
- [ ] 推理功能能正常启动
- [ ] 能显示推理结果

---

## ❌ 常见问题排查

### 安装失败
```bash
# 检查依赖问题
sudo apt-get install -f

# 检查磁盘空间
df -h

# 检查权限
sudo ls -la /opt/
```

### 摄像头问题
```bash
# 检查摄像头设备
lsusb | grep -i camera
v4l2-ctl --list-devices

# 检查权限
groups $USER | grep video

# 临时修复权限
sudo chmod 666 /dev/video*
```

### 程序启动问题
```bash
# 检查可执行文件
ls -la /opt/inference-test-app/InferenceApp

# 检查依赖库
ldd /opt/inference-test-app/InferenceApp

# 查看错误日志
tail -f /var/log/inference-test-app/app.log
```

---

## 📋 安装完成确认

### 最终检查清单
- [ ] 软件包已成功安装
- [ ] 程序能正常启动
- [ ] 摄像头功能正常
- [ ] 图片保存功能正常
- [ ] 配置文件正确
- [ ] 权限设置正确
- [ ] 桌面快捷方式可用
- [ ] 命令行快捷方式可用

### 交付文档
- [ ] 用户已收到 `USER_MANUAL.md` 完整手册
- [ ] 用户已收到 `QUICK_START.md` 快速指南
- [ ] 用户了解基本使用方法
- [ ] 用户知道技术支持联系方式

---

## 🎯 安装成功标志

当以下所有项目都完成时，安装即为成功：

1. ✅ 运行 `inference-test-app` 命令能启动程序
2. ✅ 程序界面正常显示
3. ✅ 点击"拍照"按钮能正常拍摄
4. ✅ 图片能保存到指定目录
5. ✅ 没有权限相关的错误信息

---

## 📞 支持信息

如果安装过程中遇到问题，请收集以下信息：

```bash
# 系统信息
uname -a
lsb_release -a

# 安装状态
dpkg -l | grep inference-test-app

# 错误日志
tail -20 /var/log/inference-test-app/app.log

# 权限信息
groups $USER
ls -la /opt/inference-test-app/
```

然后联系技术支持团队。

---

*此检查清单确保模型推理测试系统的正确安装和配置*
