aarch64-linux-gnu-g++ -c -pipe -fno-sized-deallocation -Wno-deprecated-declarations -O2 -O2 -std=gnu++1z -Wall -Wextra -D_REENTRANT -fPIC -DRK3588_PLATFORM -DRK3588_PLATFORM -DQT_DEPRECATED_WARNINGS -DRELEASE_MODE -DQT_NO_DEBUG -DQT_MULTIMEDIAWIDGETS_LIB -DQT_WIDGETS_LIB -DQT_MULTIMEDIA_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB -I../../test -I. -I../../test -I/usr/include/aarch64-linux-gnu/qt5 -I/usr/include/aarch64-linux-gnu/qt5/QtMultimediaWidgets -I/usr/include/aarch64-linux-gnu/qt5/QtWidgets -I/usr/include/aarch64-linux-gnu/qt5/QtMultimedia -I/usr/include/aarch64-linux-gnu/qt5/QtGui -I/usr/include/aarch64-linux-gnu/qt5/QtNetwork -I/usr/include/aarch64-linux-gnu/qt5/QtCore -Ibuild/moc -I/usr/lib/aarch64-linux-gnu/qt5/mkspecs/linux-g++ -o build/obj/modelmanager.o ../modelmanager.cpp
../modelmanager.cpp: In member function ‘void ModelManager::sendInferenceRequest(const QString&, const QString&)’:
../modelmanager.cpp:176:12: warning: unused variable ‘bytesWritten’ [-Wunused-variable]
  176 |     qint64 bytesWritten = m_demoProcess->write(requestBytes);
      |            ^~~~~~~~~~~~
../modelmanager.cpp: At global scope:
../modelmanager.cpp:392:1: error: expected declaration before ‘}’ token
  392 | }
      | ^
make: *** [Makefile:509：build/obj/modelmanager.o] 错误 1
