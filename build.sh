#!/bin/bash
# 部署模型推理测试应用编译脚本
# 适用arm64 Debian主机环境

set -e  # 遇到错误立即退出

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
echo_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
echo_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
echo_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_NAME="InferenceApp"
BUILD_DIR="${SCRIPT_DIR}/build"

# 检测系统架构和Qt路径
ARCH=$(uname -m)
if [[ "$ARCH" == "aarch64" ]]; then
    # RK3588 (ARM64) 环境
    QT_PATH="${QT_PATH:-/usr/lib/aarch64-linux-gnu/qt5}"
    if [[ ! -d "$QT_PATH" ]]; then
        QT_PATH="${QT_PATH:-/opt/Qt5.15.2/5.15.2/gcc_64}"
    fi
    echo_info "检测到ARM64架构，使用RK3588配置"
else
    # x86_64 环境
    QT_PATH="${QT_PATH:-/opt/Qt5.12.6/5.12.6/gcc_64}"
    echo_info "检测到x86_64架构，使用标准配置"
fi

echo_info "=== 部署模型推理测试应用编译脚本 ==="
echo_info "项目目录: ${SCRIPT_DIR}"
echo_info "构建目录: ${BUILD_DIR}"

# 检查Qt环境
check_qt_environment() {
    echo_info "检查Qt环境..."

    # 尝试多个可能的qmake路径
    QMAKE_CANDIDATES=(
        "${QT_PATH}/bin/qmake"
        "/usr/bin/qmake"
        "/usr/bin/qmake-qt5"
        "/usr/lib/qt5/bin/qmake"
        "/usr/lib/aarch64-linux-gnu/qt5/bin/qmake"
        "/usr/lib/x86_64-linux-gnu/qt5/bin/qmake"
        "qmake"
    )

    QMAKE_PATH=""
    for candidate in "${QMAKE_CANDIDATES[@]}"; do
        if command -v "$candidate" >/dev/null 2>&1; then
            QMAKE_PATH="$candidate"
            QT_PATH=$($candidate -query QT_INSTALL_PREFIX 2>/dev/null)
            echo_success "找到qmake: ${QMAKE_PATH}"
            echo_info "Qt安装路径: ${QT_PATH}"
            break
        fi
    done

    if [[ -z "${QMAKE_PATH}" ]]; then
        echo_error "未找到qmake"
        echo_info "请安装Qt5开发包:"
        echo_info "  sudo apt update"
        echo_info "  sudo apt install qtbase5-dev qtchooser qt5-qmake qtbase5-dev-tools"
        echo_info "  sudo apt install qtmultimedia5-dev"
        echo_info ""
        echo_info "或者运行调试脚本查看详细信息:"
        echo_info "  chmod +x debug_qt_env.sh && ./debug_qt_env.sh"
        exit 1
    fi

    # 显示Qt版本信息
    QT_VERSION=$(${QMAKE_PATH} --version 2>/dev/null | grep "Qt version" | cut -d' ' -f4)
    echo_info "Qt版本: ${QT_VERSION}"

    # 验证qmake是否正常工作
    if ! ${QMAKE_PATH} --version >/dev/null 2>&1; then
        echo_error "qmake无法正常运行: ${QMAKE_PATH}"
        exit 1
    fi
    
    # 检查必要的Qt模块
    QT_MODULES=("Core" "Gui" "Widgets" "Multimedia" "MultimediaWidgets")
    for module in "${QT_MODULES[@]}"; do
        # 尝试多个可能的库路径
        MODULE_PATHS=(
            "${QT_PATH}/lib/libQt5${module}.so"
            "/usr/lib/aarch64-linux-gnu/libQt5${module}.so"
            "/usr/lib/x86_64-linux-gnu/libQt5${module}.so"
            "/usr/lib/libQt5${module}.so"
        )

        MODULE_FOUND=false
        for MODULE_PATH in "${MODULE_PATHS[@]}"; do
            if [[ -f "${MODULE_PATH}" ]]; then
                echo_success "✓ Qt${module}模块可用: ${MODULE_PATH}"
                MODULE_FOUND=true
                break
            fi
        done

        if [[ "$MODULE_FOUND" == false ]]; then
            echo_warning "⚠️ Qt${module}模块可能不可用"
        fi
    done
}

# 清理构建目录
clean_build_directory() {
    echo_info "清理构建目录..."

    if [[ -d "${BUILD_DIR}" ]]; then
        echo_info "发现现有构建目录，正在清理..."

        # 清理编译产物，但保留数据目录
        rm -f "${BUILD_DIR}/${PROJECT_NAME}"*
        rm -f "${BUILD_DIR}/Makefile"*
        rm -f "${BUILD_DIR}/"*.o
        rm -rf "${BUILD_DIR}/obj"
        rm -rf "${BUILD_DIR}/moc"
        rm -rf "${BUILD_DIR}/rcc"
        rm -rf "${BUILD_DIR}/ui"

        echo_success "✓ 构建目录清理完成"
    fi
}

# 创建构建目录
setup_build_directory() {
    echo_info "设置构建目录..."

    # 创建构建目录
    mkdir -p "${BUILD_DIR}"

    # 创建必要的子目录
    mkdir -p "${BUILD_DIR}/obj"
    mkdir -p "${BUILD_DIR}/moc"
    mkdir -p "${BUILD_DIR}/rcc"
    mkdir -p "${BUILD_DIR}/ui"

    # 创建运行时目录（如果不存在）
    mkdir -p "${BUILD_DIR}/data"
    mkdir -p "${BUILD_DIR}/models"
    mkdir -p "${BUILD_DIR}/images"
    mkdir -p "${BUILD_DIR}/temp"
    mkdir -p "${BUILD_DIR}/logs"
    mkdir -p "${BUILD_DIR}/config"

    echo_success "构建目录设置完成"
}

# 复制资源文件
copy_resources() {
    echo_info "复制资源文件..."
    
    # 复制配置文件
    if [[ -d "${SCRIPT_DIR}/config" ]]; then
        cp -r "${SCRIPT_DIR}/config/"* "${BUILD_DIR}/config/"
        echo_success "✓ 配置文件已复制"
    fi
    
    # 复制图标和图片资源（如果存在）
    if [[ -d "${SCRIPT_DIR}/icons" ]]; then
        cp -r "${SCRIPT_DIR}/icons" "${BUILD_DIR}/"
        echo_success "✓ 图标文件已复制"
    fi
    
    if [[ -d "${SCRIPT_DIR}/images" ]]; then
        cp -r "${SCRIPT_DIR}/images" "${BUILD_DIR}/"
        echo_success "✓ 图片文件已复制"
    fi
    
    echo_success "资源文件复制完成"
}

# 编译项目
compile_project() {
    echo_info "编译项目..."

    cd "${BUILD_DIR}"

    # 设置Qt环境变量
    export PATH="${QT_PATH}/bin:${PATH}"
    export LD_LIBRARY_PATH="${QT_PATH}/lib:${LD_LIBRARY_PATH}"
    export QT_PLUGIN_PATH="${QT_PATH}/plugins"

    # 清理之前的编译产物
    if [[ -f "Makefile" ]]; then
        echo_info "清理之前的编译产物..."
        make clean 2>/dev/null || true
    fi

    # 生成Makefile
    echo_info "生成Makefile..."

    # 为RK3588添加特定的qmake参数
    if [[ "$ARCH" == "aarch64" ]]; then
        echo_info "为RK3588平台配置qmake参数..."
        ${QMAKE_PATH} "${SCRIPT_DIR}/${PROJECT_NAME}.pro" "DEFINES+=RK3588_PLATFORM"
    else
        ${QMAKE_PATH} "${SCRIPT_DIR}/${PROJECT_NAME}.pro"
    fi

    if [[ $? -ne 0 ]]; then
        echo_error "qmake失败"
        echo_info "检查项目文件和Qt环境配置"
        cd "${SCRIPT_DIR}"
        exit 1
    fi

    echo_success "✓ Makefile生成成功"

    # 编译项目
    echo_info "编译源代码..."
    make -j$(nproc) 2>&1 | tee compile.log

    if [[ ${PIPESTATUS[0]} -ne 0 ]]; then
        echo_error "编译失败"
        echo_info "编译日志已保存到: ${BUILD_DIR}/compile.log"
        echo_info "常见问题解决方案:"
        echo_info "1. 检查所有.cpp文件是否存在"
        echo_info "2. 检查Qt开发包是否完整安装"
        echo_info "3. 检查编译器版本兼容性"
        cd "${SCRIPT_DIR}"
        exit 1
    fi

    echo_success "✓ 编译成功"

    cd "${SCRIPT_DIR}"
}

# 检查编译结果
check_build_result() {
    echo_info "检查编译结果..."
    
    EXECUTABLE="${BUILD_DIR}/${PROJECT_NAME}"
    
    if [[ -f "${EXECUTABLE}" ]]; then
        echo_success "✓ 可执行文件生成成功: ${EXECUTABLE}"
        
        # 检查文件大小
        FILE_SIZE=$(du -h "${EXECUTABLE}" | cut -f1)
        echo_info "可执行文件大小: ${FILE_SIZE}"
        
        # 检查依赖库
        echo_info "检查依赖库..."
        if command -v ldd &> /dev/null; then
            MISSING_LIBS=$(ldd "${EXECUTABLE}" 2>/dev/null | grep "not found" || true)
            if [[ -n "${MISSING_LIBS}" ]]; then
                echo_warning "⚠️ 发现缺失的依赖库:"
                echo "${MISSING_LIBS}"
            else
                echo_success "✓ 所有依赖库都可用"
            fi
        fi
        
        # 设置执行权限
        chmod +x "${EXECUTABLE}"
        echo_success "✓ 执行权限已设置"
        
    else
        echo_error "可执行文件生成失败"
        exit 1
    fi
}

# 创建启动脚本
create_launch_script() {
    echo_info "创建启动脚本..."

    LAUNCH_SCRIPT="${BUILD_DIR}/run_inference_app.sh"
    OLD_LAUNCH_SCRIPT="${BUILD_DIR}/run_diagnosis_app.sh"

    # 检查是否已存在自定义的启动脚本
    if [[ -f "${LAUNCH_SCRIPT}" ]]; then
        echo_info "发现现有启动脚本，保留用户自定义设置"
        chmod +x "${LAUNCH_SCRIPT}"
    elif [[ -f "${OLD_LAUNCH_SCRIPT}" ]]; then
        echo_info "发现旧版启动脚本，重命名为新版本"
        mv "${OLD_LAUNCH_SCRIPT}" "${LAUNCH_SCRIPT}"
        chmod +x "${LAUNCH_SCRIPT}"
        echo_success "✓ 启动脚本权限已更新: ${LAUNCH_SCRIPT}"
        return
    fi

    # 创建新的启动脚本，使用与现有文件一致的设置
    cat > "${LAUNCH_SCRIPT}" << EOF
#!/bin/bash
# 部署模型推理测试应用启动脚本

# 设置工作目录
cd "\$(dirname "\${BASH_SOURCE[0]}")"

# 设置Qt环境变量
export LD_LIBRARY_PATH="/usr/lib/aarch64-linux-gnu/qt5/lib:\${LD_LIBRARY_PATH}"
export QT_PLUGIN_PATH="/usr/lib/aarch64-linux-gnu/qt5/plugins"
export LD_LIBRARY_PATH="/userdata/install/demo_Linux_aarch64/lib:\${LD_LIBRARY_PATH}"

# 设置字体环境变量
export QT_QPA_FONTDIR="/usr/share/fonts"
export FONTCONFIG_PATH="/etc/fonts"

# 启动应用程序
echo "启动部署模型推理测试系统..."
./${PROJECT_NAME}
EOF

    chmod +x "${LAUNCH_SCRIPT}"
    echo_success "✓ 启动脚本已创建: ${LAUNCH_SCRIPT}"
}

# 显示使用说明
show_usage_info() {
    echo_info "=== 编译完成 ==="
    echo_success "🎉 部署模型推理测试应用编译成功！"
    
    echo_info "=== 使用说明 ==="
    echo_info "1. 直接运行:"
    echo_info "   cd ${BUILD_DIR} && ./${PROJECT_NAME}"
    echo_info ""
    echo_info "2. 使用启动脚本:"
    echo_info "   ${BUILD_DIR}/run_inference_app.sh"
    echo_info ""
    echo_info "3. 设置环境变量后运行:"
    echo_info "   export LD_LIBRARY_PATH=${QT_PATH}/lib:\$LD_LIBRARY_PATH"
    echo_info "   ${BUILD_DIR}/${PROJECT_NAME}"
    
    echo_info "=== 文件位置 ==="
    echo_info "可执行文件: ${BUILD_DIR}/${PROJECT_NAME}"
    echo_info "配置文件: ${BUILD_DIR}/config/"
    echo_info "启动脚本: ${BUILD_DIR}/run_inference_app.sh"
}

# 检查源文件完整性
check_source_files() {
    echo_info "检查源文件完整性..."

    # 必需的源文件列表
    REQUIRED_FILES=(
        "InferenceApp.pro"
        "main.cpp"
        "mainwindow.h"
        "mainwindow.cpp"
        "taskpage.h"
        "taskpage.cpp"
        "taskmanager.h"
        "taskmanager.cpp"
        "modelinference.h"
        "modelinference.cpp"
        "camerawidget.h"
        "camerawidget.cpp"
        "taskdata.h"
    )

    local missing_files=()

    for file in "${REQUIRED_FILES[@]}"; do
        if [[ ! -f "${SCRIPT_DIR}/${file}" ]]; then
            missing_files+=("${file}")
        else
            echo_success "✓ ${file}"
        fi
    done

    if [[ ${#missing_files[@]} -gt 0 ]]; then
        echo_error "缺少以下必需文件:"
        for file in "${missing_files[@]}"; do
            echo_error "  - ${file}"
        done
        echo_info "请确保所有源文件都已创建"
        exit 1
    fi

    echo_success "源文件检查完成"
}

# 主函数
main() {
    echo_info "开始编译部署模型推理测试应用..."

    # 执行编译步骤
    check_source_files
    check_qt_environment
    clean_build_directory
    setup_build_directory
    copy_resources
    compile_project
    check_build_result
    create_launch_script
    show_usage_info

    echo_success "编译脚本执行完成！"
}

# 执行主函数
main "$@"
