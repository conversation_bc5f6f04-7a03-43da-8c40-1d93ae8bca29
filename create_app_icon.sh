#!/bin/bash
# 创建应用程序图标脚本

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ICONS_DIR="${SCRIPT_DIR}/icons"

echo "创建应用程序图标..."

# 创建图标目录
mkdir -p "${ICONS_DIR}"

# 创建简单的SVG图标（如果没有现成的图标）
if [[ ! -f "${ICONS_DIR}/app-icon.png" ]]; then
    echo "创建默认应用程序图标..."
    
    # 创建SVG图标
    cat > "${ICONS_DIR}/app-icon.svg" << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="128" height="128" rx="16" fill="#2E7D32"/>
  
  <!-- 药盒图标 -->
  <rect x="24" y="32" width="80" height="64" rx="8" fill="#FFFFFF" stroke="#1B5E20" stroke-width="2"/>
  
  <!-- 十字标志 -->
  <rect x="58" y="48" width="12" height="32" fill="#F44336"/>
  <rect x="48" y="58" width="32" height="12" fill="#F44336"/>
  
  <!-- 文字 -->
  <text x="64" y="116" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#FFFFFF">药盒识别</text>
</svg>
EOF
    
    # 如果系统有ImageMagick，转换为PNG
    if command -v convert >/dev/null 2>&1; then
        convert "${ICONS_DIR}/app-icon.svg" -resize 128x128 "${ICONS_DIR}/app-icon.png"
        echo "✓ PNG图标已生成"
    else
        echo "⚠️ 未找到ImageMagick，请手动转换SVG为PNG格式"
        echo "   或安装ImageMagick: sudo apt install imagemagick"
    fi
fi

# 创建不同尺寸的图标
if [[ -f "${ICONS_DIR}/app-icon.png" ]] && command -v convert >/dev/null 2>&1; then
    echo "创建不同尺寸的图标..."
    
    for size in 16 24 32 48 64 96 128 256; do
        convert "${ICONS_DIR}/app-icon.png" -resize ${size}x${size} "${ICONS_DIR}/app-icon-${size}.png"
    done
    
    echo "✓ 多尺寸图标已生成"
fi

echo "应用程序图标创建完成！"
