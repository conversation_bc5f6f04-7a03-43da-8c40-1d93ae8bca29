# 模型推理测试应用 - Qt项目文件
# 基于autobit_back配置，适用于RK3588边缘设备
# Qt版本：5.15.2，系统：Debian

# Qt配置 - 使用标准模块名
QT       += core gui widgets multimedia multimediawidgets network
TARGET   = InferenceApp
CONFIG   += c++17

# RK3588特定配置
DEFINES += RK3588_PLATFORM

# 禁用 sized-deallocation，防止库里对 operator delete(void*,size_t) 的依赖
QMAKE_CXXFLAGS += -fno-sized-deallocation

# Qt 5.15.2兼容性配置
QMAKE_CXXFLAGS += -Wno-deprecated-declarations

# 资源、源码、头文件列表
RESOURCES += InferenceApp.qrc

SOURCES  += main.cpp \
            mainwindow.cpp \
            taskpage.cpp \
            taskmanager.cpp \
            modelinference.cpp \
            modelmanager.cpp \
            camerawidget.cpp \
            configmanager.cpp

HEADERS  += mainwindow.h \
            taskpage.h \
            taskmanager.h \
            modelinference.h \
            modelmanager.h \
            camerawidget.h \
            taskdata.h \
            configmanager.h

# 包含目录
INCLUDEPATH += .

# 运行时库路径设置已移除，不再需要外部激活库

# 定义宏
DEFINES += QT_DEPRECATED_WARNINGS

# 输出目录
DESTDIR = build
OBJECTS_DIR = build/obj
MOC_DIR = build/moc
RCC_DIR = build/rcc
UI_DIR = build/ui

# 安装配置
target.path = /usr/local/bin
INSTALLS += target

# 调试信息
CONFIG(debug, debug|release) {
    DEFINES += DEBUG_MODE
    TARGET = $$TARGET"_debug"
}

# 发布配置
CONFIG(release, debug|release) {
    DEFINES += RELEASE_MODE
    QMAKE_CXXFLAGS += -O2
}
