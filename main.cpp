#include <QApplication>
#include <QStyleFactory>
#include <QDir>
#include <QStandardPaths>
#include <QDebug>
#include <QFont>
#include <QFontDatabase>
#include <QProcess>
#include <QTimer>
#include <csignal>
#include "mainwindow.h"

// 全局主窗口指针，用于信号处理
MainWindow* g_mainWindow = nullptr;

/**
 * @brief 信号处理函数，确保程序优雅退出
 */
void signalHandler(int signal) {
    qDebug() << "[调试] 接收到信号:" << signal;

    if (g_mainWindow) {
        qDebug() << "[调试] 开始关闭程序";
        g_mainWindow->close();
    }

    // 强制退出所有进程
    QList<QProcess*> allProcesses = QCoreApplication::instance()->findChildren<QProcess*>();
    for (QProcess* process : allProcesses) {
        if (process && process->state() != QProcess::NotRunning) {
            qDebug() << "[调试] 信号处理器终止进程:" << process;
            process->kill();
        }
    }

    QCoreApplication::quit();
}

/**
 * @brief 设置应用程序样式
 */
void setupApplicationStyle(QApplication& app) {
    // 设置应用程序样式
    app.setStyle(QStyleFactory::create("Fusion"));
    
    // 设置浅蓝色主题样式表
    QString styleSheet = R"(
        QMainWindow {
            background-color: #e6f3ff;
            color: #000000;
        }

        QWidget {
            background-color: #e6f3ff;
            color: #000000;
            font-family: "Microsoft YaHei", "SimHei", sans-serif;
            font-size: 12px;
        }

        QPushButton {
            background-color: #0078d4;
            border: 2px solid #005a9e;
            border-radius: 8px;
            padding: 8px 16px;
            font-weight: bold;
            min-height: 30px;
            color: #ffffff;
        }

        QPushButton:hover {
            background-color: #106ebe;
            border-color: #004578;
        }

        QPushButton:pressed {
            background-color: #005a9e;
            border-color: #003d6b;
        }

        QPushButton:disabled {
            background-color: #cccccc;
            border-color: #999999;
            color: #666666;
        }

        QPushButton#primaryButton {
            background-color: #0078d4;
            border-color: #106ebe;
        }
        
        QPushButton#primaryButton:hover {
            background-color: #106ebe;
            border-color: #005a9e;
        }
        
        QPushButton#primaryButton:pressed {
            background-color: #005a9e;
        }
        
        QTextEdit, QPlainTextEdit {
            background-color: #ffffff;
            border: 2px solid #cccccc;
            border-radius: 6px;
            padding: 8px;
            color: #000000;
            selection-background-color: #0078d4;
        }

        QLineEdit {
            background-color: #ffffff;
            border: 2px solid #cccccc;
            border-radius: 6px;
            padding: 8px;
            color: #000000;
            min-height: 20px;
        }

        QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
            border-color: #0078d4;
        }

        QLabel {
            color: #000000;
            background-color: transparent;
        }

        QLabel#titleLabel {
            font-size: 18px;
            font-weight: bold;
            color: #0078d4;
        }

        QLabel#guidanceLabel {
            font-size: 16px;
            color: #000000;
            background-color: #ffffff;
            border: 1px solid #cccccc;
            border-radius: 6px;
            padding: 12px;
        }
        
        QScrollArea {
            background-color: #e6f3ff;
            border: none;
        }

        QScrollBar:vertical {
            background-color: #ffffff;
            width: 12px;
            border-radius: 6px;
        }

        QScrollBar::handle:vertical {
            background-color: #cccccc;
            border-radius: 6px;
            min-height: 20px;
        }

        QScrollBar::handle:vertical:hover {
            background-color: #0078d4;
        }

        QFrame {
            background-color: #ffffff;
            border: 1px solid #cccccc;
            border-radius: 8px;
        }

        QFrame#imageFrame {
            background-color: #f8f9fa;
            border: 2px dashed #cccccc;
            border-radius: 8px;
        }

        QFrame#resultFrame {
            background-color: #ffffff;
            border: 2px solid #0078d4;
            border-radius: 8px;
        }

        QMessageBox {
            background-color: #ffffff;
            color: #000000;
        }

        QMessageBox QLabel {
            color: #000000;
        }

        QMessageBox QPushButton {
            background-color: #0078d4;
            color: #ffffff;
            border: 1px solid #005a9e;
            border-radius: 4px;
            padding: 6px 12px;
            min-width: 60px;
        }
        
        QTabWidget::pane {
            border: 1px solid #cccccc;
            background-color: #ffffff;
        }

        QTabBar::tab {
            background-color: #ffffff;
            border: 1px solid #cccccc;
            padding: 8px 16px;
            margin-right: 2px;
            color: #000000;
        }

        QTabBar::tab:selected {
            background-color: #0078d4;
            border-color: #106ebe;
            color: #ffffff;
        }

        QTabBar::tab:hover {
            background-color: #e6f3ff;
        }

        QProgressBar {
            background-color: #ffffff;
            border: 2px solid #cccccc;
            border-radius: 6px;
            text-align: center;
            color: #000000;
        }

        QProgressBar::chunk {
            background-color: #0078d4;
            border-radius: 4px;
        }
    )";
    
    app.setStyleSheet(styleSheet);
}

/**
 * @brief 显示启动信息
 */
void showStartupInfo() {
    qDebug() << "=== 模型推理测试系统启动 ===";
    qDebug() << "Qt版本:" << QT_VERSION_STR;
    qDebug() << "应用程序路径:" << QCoreApplication::applicationDirPath();
}

/**
 * @brief 清空图片保存目录
 */
void clearSavedImagesDirectory() {
    QString savedImagesDir = "/opt/inference-test-app/saved_images";
    QDir dir(savedImagesDir);

    if (dir.exists()) {
        qDebug() << "清空图片保存目录:" << savedImagesDir;

        // 获取目录中的所有图片文件
        QStringList filters;
        filters << "*.jpg" << "*.jpeg" << "*.png" << "*.bmp" << "*.gif";
        QFileInfoList fileList = dir.entryInfoList(filters, QDir::Files);

        int deletedCount = 0;
        for (const QFileInfo& fileInfo : fileList) {
            if (QFile::remove(fileInfo.absoluteFilePath())) {
                deletedCount++;
            } else {
                qWarning() << "无法删除文件:" << fileInfo.absoluteFilePath();
            }
        }

        qDebug() << "已删除" << deletedCount << "个图片文件";
    } else {
        qDebug() << "图片保存目录不存在，将在首次使用时创建:" << savedImagesDir;
    }
}

/**
 * @brief 创建应用程序目录
 */
void createAppDirectories() {
    QStringList dirs = {
        "data",
        "models",
        "images",
        "temp",
        "logs",
        "config",
        "/opt/inference-test-app/saved_images"  // 添加图片保存目录
    };

    for (const QString& dir : dirs) {
        QDir().mkpath(dir);
    }
}

/**
 * @brief 设置应用程序信息
 */
void setupApplicationInfo(QApplication& app) {
    app.setApplicationName("部署模型推理测试系统");
    app.setApplicationVersion("1.0.0");
    app.setOrganizationName("Medical AI Solutions");
    app.setOrganizationDomain("medical-ai.com");
    
    // 设置应用程序图标（如果存在）
    QIcon appIcon(":/icons/app_icon.png");
    if (!appIcon.isNull()) {
        app.setWindowIcon(appIcon);
    }
}

/**
 * @brief 主函数
 */
int main(int argc, char *argv[])
{
    QApplication app(argc, argv);

    // 注册信号处理器，确保程序能够优雅退出
    std::signal(SIGINT, signalHandler);   // Ctrl+C
    std::signal(SIGTERM, signalHandler);  // 终止信号
    #ifdef SIGQUIT
    std::signal(SIGQUIT, signalHandler);  // 退出信号
    #endif

    // 设置应用程序信息
    setupApplicationInfo(app);

    // 显示启动信息
    showStartupInfo();

    // 创建必要的目录
    createAppDirectories();

    // 清空图片保存目录（启动时清理）
    clearSavedImagesDirectory();

    // 设置应用程序样式
    setupApplicationStyle(app);

    // 设置字体
    QFont font("Microsoft YaHei", 10);
    app.setFont(font);

    // 创建主窗口但不立即显示
    MainWindow window;
    g_mainWindow = &window;

    // 连接模型加载完成信号，在模型加载完成后显示主窗口
    QObject::connect(&window, &MainWindow::modelLoadingCompleted, [&window]() {
        qDebug() << "模型加载完成，显示主窗口";
        window.show();
    });

    // 开始初始化（包括模型加载）
    window.startInitialization();

    qDebug() << "部署模型推理测试系统启动成功";
    qDebug() << "Qt版本:" << QT_VERSION_STR;
    qDebug() << "应用程序路径:" << QCoreApplication::applicationDirPath();

    int result = app.exec();

    // 清理全局指针
    g_mainWindow = nullptr;

    qDebug() << "[调试] 应用程序正常退出";
    return result;
}
