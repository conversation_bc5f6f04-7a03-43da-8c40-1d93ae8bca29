#!/bin/bash
# 部署模型推理测试系统 - 卸载问题调试脚本
# 用于调试和修复卸载过程中的问题

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
echo_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
echo_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
echo_error() { echo -e "${RED}[ERROR]${NC} $1"; }

PACKAGE_NAME="diagnosis-app"

echo_info "=== 卸载问题调试脚本 ==="

# 检查包状态
check_package_status() {
    echo_info "检查包状态..."
    
    if dpkg -l | grep -q "$PACKAGE_NAME"; then
        echo_info "包状态:"
        dpkg -l | grep "$PACKAGE_NAME"
        
        # 检查包状态标志
        local status=$(dpkg -l | grep "$PACKAGE_NAME" | awk '{print $1}')
        case "$status" in
            "ii") echo_success "包已正常安装" ;;
            "rc") echo_warning "包已删除但配置文件保留" ;;
            "rF") echo_error "包卸载失败" ;;
            "iF") echo_error "包安装失败" ;;
            *) echo_warning "未知状态: $status" ;;
        esac
    else
        echo_info "包未安装或已完全卸载"
    fi
}

# 检查相关进程
check_processes() {
    echo_info "检查相关进程..."
    
    local diagnosis_pids=$(pgrep -f "DiagnosisApp" 2>/dev/null || true)
    local app_pids=$(pgrep -f "diagnosis-app" 2>/dev/null || true)
    
    if [[ -n "$diagnosis_pids" ]]; then
        echo_warning "发现DiagnosisApp进程:"
        ps aux | grep -E "DiagnosisApp" | grep -v grep
    else
        echo_success "未发现DiagnosisApp进程"
    fi
    
    if [[ -n "$app_pids" ]]; then
        echo_warning "发现diagnosis-app进程:"
        ps aux | grep -E "diagnosis-app" | grep -v grep
    else
        echo_success "未发现diagnosis-app进程"
    fi
}

# 检查文件状态
check_files() {
    echo_info "检查文件状态..."
    
    local files_to_check=(
        "/opt/diagnosis-app"
        "/usr/local/bin/diagnosis-app"
        "/usr/share/applications/diagnosis-app.desktop"
        "/etc/environment.d/diagnosis-app.conf"
        "/var/lib/dpkg/info/diagnosis-app.prerm"
        "/var/lib/dpkg/info/diagnosis-app.postrm"
        "/var/lib/dpkg/info/diagnosis-app.postinst"
    )
    
    for file in "${files_to_check[@]}"; do
        if [[ -e "$file" ]]; then
            if [[ -d "$file" ]]; then
                echo_info "目录存在: $file"
                ls -la "$file" | head -5
            else
                echo_info "文件存在: $file"
                ls -la "$file"
            fi
        else
            echo_success "文件不存在: $file"
        fi
    done
}

# 检查dpkg状态
check_dpkg_status() {
    echo_info "检查dpkg状态..."
    
    # 检查dpkg是否有问题
    if sudo dpkg --configure -a 2>&1 | grep -q "error\|failed"; then
        echo_warning "dpkg配置有问题"
        sudo dpkg --configure -a
    else
        echo_success "dpkg状态正常"
    fi
    
    # 检查包的详细状态
    if [[ -f "/var/lib/dpkg/status" ]]; then
        echo_info "包在dpkg状态文件中的记录:"
        grep -A 10 "Package: $PACKAGE_NAME" /var/lib/dpkg/status || echo_info "未找到包记录"
    fi
}

# 手动测试卸载脚本
test_uninstall_scripts() {
    echo_info "测试卸载脚本..."
    
    local prerm_script="/var/lib/dpkg/info/diagnosis-app.prerm"
    local postrm_script="/var/lib/dpkg/info/diagnosis-app.postrm"
    
    if [[ -f "$prerm_script" ]]; then
        echo_info "测试prerm脚本..."
        echo_info "脚本内容预览:"
        head -20 "$prerm_script"
        echo_info "手动执行prerm脚本..."
        sudo bash -x "$prerm_script" remove 2>&1 | head -20
    else
        echo_warning "prerm脚本不存在"
    fi
    
    if [[ -f "$postrm_script" ]]; then
        echo_info "测试postrm脚本..."
        echo_info "脚本内容预览:"
        head -20 "$postrm_script"
    else
        echo_warning "postrm脚本不存在"
    fi
}

# 强制清理
force_cleanup() {
    echo_warning "执行强制清理..."
    
    # 停止所有相关进程
    echo_info "停止所有相关进程..."
    sudo pkill -f "DiagnosisApp" 2>/dev/null || true
    sudo pkill -f "diagnosis-app" 2>/dev/null || true
    sleep 2
    sudo pkill -9 -f "DiagnosisApp" 2>/dev/null || true
    sudo pkill -9 -f "diagnosis-app" 2>/dev/null || true
    
    # 强制删除包记录
    echo_info "强制删除包记录..."
    sudo dpkg --remove --force-remove-reinstreq "$PACKAGE_NAME" 2>/dev/null || true
    sudo dpkg --purge --force-remove-reinstreq "$PACKAGE_NAME" 2>/dev/null || true
    
    # 手动清理文件
    echo_info "手动清理文件..."
    sudo rm -rf /opt/diagnosis-app 2>/dev/null || true
    sudo rm -f /usr/local/bin/diagnosis-app 2>/dev/null || true
    sudo rm -f /usr/share/applications/diagnosis-app.desktop 2>/dev/null || true
    sudo rm -f /etc/environment.d/diagnosis-app.conf 2>/dev/null || true
    
    # 清理用户和组
    echo_info "清理用户和组..."
    sudo userdel diagnosis 2>/dev/null || true
    sudo groupdel diagnosis 2>/dev/null || true
    
    # 清理dpkg信息
    echo_info "清理dpkg信息..."
    sudo rm -f /var/lib/dpkg/info/diagnosis-app.* 2>/dev/null || true
    
    # 更新dpkg状态
    sudo dpkg --configure -a 2>/dev/null || true
    
    echo_success "强制清理完成"
}

# 主菜单
show_menu() {
    echo ""
    echo_info "请选择操作:"
    echo "1. 检查包状态"
    echo "2. 检查相关进程"
    echo "3. 检查文件状态"
    echo "4. 检查dpkg状态"
    echo "5. 测试卸载脚本"
    echo "6. 强制清理"
    echo "7. 完整诊断"
    echo "8. 退出"
    echo ""
    read -p "请输入选择 (1-8): " choice
    
    case $choice in
        1) check_package_status ;;
        2) check_processes ;;
        3) check_files ;;
        4) check_dpkg_status ;;
        5) test_uninstall_scripts ;;
        6) 
            echo_warning "这将强制清理所有相关文件和配置，确定继续吗? (y/N)"
            read -r confirm
            if [[ "$confirm" =~ ^[Yy]$ ]]; then
                force_cleanup
            else
                echo_info "取消强制清理"
            fi
            ;;
        7)
            echo_info "执行完整诊断..."
            check_package_status
            check_processes
            check_files
            check_dpkg_status
            ;;
        8) echo_info "退出"; exit 0 ;;
        *) echo_error "无效选择" ;;
    esac
}

# 主循环
main() {
    while true; do
        show_menu
        echo ""
        read -p "按回车键继续..."
    done
}

# 如果有命令行参数，直接执行对应功能
if [[ $# -gt 0 ]]; then
    case "$1" in
        "status") check_package_status ;;
        "processes") check_processes ;;
        "files") check_files ;;
        "dpkg") check_dpkg_status ;;
        "scripts") test_uninstall_scripts ;;
        "cleanup") force_cleanup ;;
        "diagnose") 
            check_package_status
            check_processes
            check_files
            check_dpkg_status
            ;;
        *) echo_error "未知参数: $1" ;;
    esac
else
    main
fi
