{
    "version": "1.0",
    "system_name": "部署模型推理测试系统",
    "description": "通用图像推理测试平台",
    "lastModified": "2024-12-01T10:00:00",
    "tasks": [
        {
            "taskId": 1,
            "taskName": "通用图像识别",
            "guidanceText": "欢迎使用通用图像识别功能！\n\n请按照以下步骤操作：\n1. 拍摄或上传清晰的图片\n2. 在下方输入相关描述信息\n3. 点击\"开始推理\"按钮进行模型推理\n\n注意：请确保图片清晰，内容完整可见。",
            "symptomPlaceholder": "请详细描述图片内容或测试要求，例如：物体识别、场景分析、特征检测等...",
            "inferenceType": "通用推理",
            "expectedFeatures": ["物体识别", "场景分析", "特征检测", "图像分类", "目标检测"],
            "modelPath": "models/general_inference_model.rknn",
            "configPath": "config/general_inference_config.json",
            "isActive": true
        },
        {
            "taskId": 2,
            "taskName": "目标检测测试",
            "guidanceText": "欢迎使用目标检测测试功能！\n\n请按照以下步骤操作：\n1. 拍摄或上传包含目标对象的图片\n2. 在下方输入检测目标的描述\n3. 点击\"开始推理\"按钮进行目标检测\n\n注意：请确保图片清晰，目标对象完整可见。",
            "symptomPlaceholder": "请详细描述要检测的目标，例如：人物检测、车辆识别、物体定位等...",
            "inferenceType": "目标检测",
            "expectedFeatures": ["人物检测", "车辆识别", "物体定位", "边界框检测", "多目标识别"],
            "modelPath": "models/object_detection_model.rknn",
            "configPath": "config/object_detection_config.json",
            "isActive": true
        },
        {
            "taskId": 3,
            "taskName": "图像分类测试",
            "guidanceText": "欢迎使用图像分类测试功能！\n\n请按照以下步骤操作：\n1. 拍摄或上传待分类的图片\n2. 在下方输入分类相关信息\n3. 点击\"开始推理\"按钮进行图像分类\n\n注意：请确保图片清晰，内容特征明显。",
            "symptomPlaceholder": "请详细描述分类要求，例如：动物分类、植物识别、场景分类等...",
            "inferenceType": "图像分类",
            "expectedFeatures": ["动物分类", "植物识别", "场景分类", "物品分类", "特征识别"],
            "modelPath": "models/image_classification_model.rknn",
            "configPath": "config/image_classification_config.json",
            "isActive": true
        },
        {
            "taskId": 4,
            "taskName": "胃药识别",
            "guidanceText": "欢迎使用胃药识别功能！\n\n请按照以下步骤操作：\n1. 拍摄或上传清晰的胃药包装照片\n2. 在下方输入您的胃部症状描述\n3. 点击"开始推理"按钮获取用药建议\n\n注意：请确保照片清晰，药盒包装完整可见。",
            "symptomPlaceholder": "请详细描述您的胃部症状，例如：胃痛、胃胀、消化不良、胃酸过多、恶心呕吐等...",
            "medicineType": "胃药",
            "expectedSymptoms": ["胃痛", "胃胀", "消化不良", "胃酸", "恶心", "呕吐", "胃灼热", "食欲不振"],
            "modelPath": "models/stomach_medicine_model.rknn",
            "configPath": "config/stomach_medicine_config.json",
            "isActive": true
        },
        {
            "taskId": 5,
            "taskName": "特征提取测试",
            "guidanceText": "欢迎使用特征提取测试功能！\n\n请按照以下步骤操作：\n1. 拍摄或上传待分析的图片\n2. 在下方输入特征提取要求\n3. 点击\"开始推理\"按钮进行特征提取\n\n注意：请确保图片清晰，特征明显。",
            "symptomPlaceholder": "请详细描述特征提取要求，例如：边缘检测、纹理分析、颜色特征、形状识别等...",
            "inferenceType": "特征提取",
            "expectedFeatures": ["边缘检测", "纹理分析", "颜色特征", "形状识别", "关键点检测"],
            "modelPath": "models/feature_extraction_model.rknn",
            "configPath": "config/feature_extraction_config.json",
            "isActive": true
        }
    ]
}
