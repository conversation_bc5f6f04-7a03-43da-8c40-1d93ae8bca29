# 模型推理测试系统快速安装指南

## 🚀 快速开始

### 在开发环境中构建 .deb 包

```bash
# 1. 克隆或准备项目代码
cd /path/to/diagnosis-app

# 2. 一键构建和打包
chmod +x build_and_package.sh
./build_and_package.sh
```

### 在目标 arm64 Debian 系统中安装

```bash
# 1. 传输 .deb 包到目标系统
scp inference-test-app_1.0.0_arm64.deb user@target-system:/tmp/

# 2. 在目标系统上安装
ssh user@target-system
cd /tmp
sudo dpkg -i inference-test-app_1.0.0_arm64.deb
sudo apt-get install -f  # 修复依赖（如果需要）

# 3. 运行应用
inference-test-app
```

## 📋 系统要求

- **系统**: arm64 Debian 11+
- **Qt**: 5.15.2+
- **内存**: 最少 512MB
- **存储**: 最少 100MB 可用空间

## ⚙️ 配置文件

安装后，主配置文件位于：`/etc/inference-test-app/inference.conf`

### 常用配置项

```ini
[RK3588_Inference]
# 修改模型路径
rknn_model_path=/your/model/path/model.rknn
rkllm_model_path=/your/model/path/model.rkllm

# 调整推理参数
max_tokens=256        # 增加最大令牌数
context_length=1024   # 增加上下文长度

[General]
# 调整超时时间
timeout_seconds=600   # 10分钟超时
```

## 🔧 常用命令

```bash
# 启动应用
inference-test-app

# 查看日志
tail -f /var/log/inference-test-app/app.log

# 重新加载配置（重启应用）
pkill DiagnosisApp && inference-test-app

# 卸载应用
sudo apt remove inference-test-app      # 保留配置
sudo apt purge inference-test-app       # 完全删除
```

## 🐛 故障排除

### 1. 应用无法启动
```bash
# 检查依赖
ldd /opt/inference-test-app/DiagnosisApp

# 手动运行查看错误
cd /opt/inference-test-app
./DiagnosisApp
```

### 2. 模型文件找不到
```bash
# 检查模型文件路径
ls -la /userdata/models/

# 更新配置文件中的路径
sudo nano /etc/inference-test-app/inference.conf
```

### 3. 权限问题
```bash
# 修复日志目录权限
sudo chown -R $USER:$USER /var/log/inference-test-app

# 修复应用目录权限
sudo chmod -R 755 /opt/inference-test-app
```

## 📞 支持

如需技术支持，请提供：
1. 系统信息：`uname -a`
2. Qt版本：`qmake --version`
3. 错误日志：`/var/log/diagnosis-app/app.log`
4. 配置文件：`/etc/diagnosis-app/inference.conf`
