{"version": "1.0", "system_name": "部署模型推理测试系统", "description": "通用图像推理测试平台", "models": {"rknn_model": "models/vision_model.rknn", "rkllm_model": "models/llm_model.rkllm", "max_tokens": 128, "context_length": 512}, "tasks": [{"taskId": 1, "taskName": "通用图像识别", "guidanceText": "欢迎使用通用图像识别功能！\n\n请按照以下步骤操作：\n1. 拍摄或上传清晰的图片\n2. 在下方输入相关描述信息\n3. 点击\"开始推理\"按钮进行模型推理\n\n注意：请确保图片清晰，内容完整可见。", "symptomPlaceholder": "请详细描述图片内容或测试要求，例如：物体识别、场景分析、特征检测等...", "inferenceType": "通用推理", "expectedFeatures": ["物体识别", "场景分析", "特征检测", "图像分类", "目标检测"], "modelPath": "models/general_inference_model.rknn", "configPath": "config/general_inference_config.json", "isActive": true}], "questions_file": "config/questions.json"}