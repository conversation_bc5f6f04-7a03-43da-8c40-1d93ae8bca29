#ifndef CAMERAWIDGET_H
#define CAMERAWIDGET_H

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QLabel>
#include <QComboBox>
#include <QTimer>
#include <QCamera>
#include <QCameraViewfinder>
#include <QCameraImageCapture>
#include <QCameraInfo>
#include <QImageEncoderSettings>
#include <QPixmap>
#include <QFileDialog>
#include <QStandardPaths>
#include <QDir>
#include <QDebug>

/**
 * @brief 相机控件类
 * 提供拍照和图片预览功能
 */
class CameraWidget : public QWidget
{
    Q_OBJECT

public:
    explicit CameraWidget(QWidget *parent = nullptr);
    ~CameraWidget();

    // 开始相机预览
    void startCamera();
    
    // 停止相机预览
    void stopCamera();
    
    // 拍照
    void captureImage();
    
    // 检查相机是否可用
    bool isCameraAvailable() const;
    
    // 获取可用相机列表
    QStringList getAvailableCameras() const;
    
    // 设置当前相机
    void setCurrentCamera(int index);
    
    // 获取最后拍摄的图片路径
    QString getLastCapturedImagePath() const { return m_lastCapturedImagePath; }

signals:
    void imageCaptured(const QString& imagePath);
    void cameraError(const QString& errorMessage);
    void cameraStateChanged(bool isActive);

private slots:
    void onCameraStateChanged(QCamera::State state);
    void onCameraError(QCamera::Error error);
    void onImageCaptured(int id, const QImage& image);
    void onImageSaved(int id, const QString& fileName);
    void onCaptureError(int id, QCameraImageCapture::Error error, const QString& errorString);
    void onStartStopClicked();
    void onCaptureClicked();
    void onCameraSelectionChanged(int index);

private:
    void setupUI();
    void setupCamera();
    void connectSignals();
    void updateCameraList();
    void updateUI();
    QString createImageSavePath() const;
    void showCameraError(const QString& errorMessage);
    
    // UI组件
    QVBoxLayout *m_mainLayout;
    QHBoxLayout *m_controlLayout;
    QCameraViewfinder *m_viewfinder;
    QComboBox *m_cameraComboBox;
    QPushButton *m_startStopButton;
    QPushButton *m_captureButton;
    QLabel *m_statusLabel;
    
    // 相机组件
    QCamera *m_camera;
    QCameraImageCapture *m_imageCapture;
    QList<QCameraInfo> m_availableCameras;
    
    // 状态变量
    bool m_isCameraActive;
    QString m_lastCapturedImagePath;
    QString m_imagesSaveDir;
    int m_currentCameraIndex;
    
    // 常量
    static const int VIEWFINDER_WIDTH = 640;
    static const int VIEWFINDER_HEIGHT = 480;
    static const QString IMAGES_DIR_NAME;
};

#endif // CAMERAWIDGET_H
