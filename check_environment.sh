#!/bin/bash
# 模型推理测试系统环境检查脚本
# 适用于arm64 Debian系统

REPORT_FILE="environment_check_report.txt"
TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查结果统计
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0
WARNING_CHECKS=0

# 初始化报告文件
init_report() {
    cat > "$REPORT_FILE" << EOF
================================================================================
模型推理测试系统环境检查报告
================================================================================
检查时间: $TIMESTAMP
系统架构: $(uname -m)
操作系统: $(lsb_release -d 2>/dev/null | cut -f2 || echo "Unknown")
内核版本: $(uname -r)
================================================================================

EOF
}

# 记录检查结果
log_check() {
    local status="$1"
    local item="$2"
    local details="$3"
    local recommendation="$4"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    case "$status" in
        "PASS")
            PASSED_CHECKS=$((PASSED_CHECKS + 1))
            echo -e "${GREEN}✓ PASS${NC}: $item"
            echo "✓ PASS: $item" >> "$REPORT_FILE"
            ;;
        "FAIL")
            FAILED_CHECKS=$((FAILED_CHECKS + 1))
            echo -e "${RED}✗ FAIL${NC}: $item"
            echo "✗ FAIL: $item" >> "$REPORT_FILE"
            ;;
        "WARN")
            WARNING_CHECKS=$((WARNING_CHECKS + 1))
            echo -e "${YELLOW}⚠ WARN${NC}: $item"
            echo "⚠ WARN: $item" >> "$REPORT_FILE"
            ;;
    esac
    
    if [ -n "$details" ]; then
        echo "  详情: $details" >> "$REPORT_FILE"
    fi
    
    if [ -n "$recommendation" ]; then
        echo "  建议: $recommendation" >> "$REPORT_FILE"
    fi
    
    echo "" >> "$REPORT_FILE"
}

# 检查系统架构
check_architecture() {
    echo -e "\n${BLUE}=== 检查系统架构 ===${NC}"
    echo "=== 检查系统架构 ===" >> "$REPORT_FILE"
    
    local arch=$(uname -m)
    if [ "$arch" = "aarch64" ]; then
        log_check "PASS" "系统架构" "当前架构: $arch (arm64兼容)"
    else
        log_check "FAIL" "系统架构" "当前架构: $arch" "需要arm64(aarch64)架构"
    fi
}

# 检查操作系统
check_os() {
    echo -e "\n${BLUE}=== 检查操作系统 ===${NC}"
    echo "=== 检查操作系统 ===" >> "$REPORT_FILE"
    
    if [ -f /etc/debian_version ]; then
        local debian_version=$(cat /etc/debian_version)
        log_check "PASS" "操作系统" "Debian系统，版本: $debian_version"
    else
        log_check "WARN" "操作系统" "非Debian系统" "推荐使用Debian系统"
    fi
}

# 检查Qt5环境
check_qt5() {
    echo -e "\n${BLUE}=== 检查Qt5环境 ===${NC}"
    echo "=== 检查Qt5环境 ===" >> "$REPORT_FILE"
    
    # 检查Qt5开发包
    if dpkg -l | grep -q "qtbase5-dev"; then
        log_check "PASS" "Qt5开发包" "qtbase5-dev已安装"
    else
        log_check "FAIL" "Qt5开发包" "qtbase5-dev未安装" "需要安装Qt5开发包"
    fi
    
    # 检查Qt5多媒体包
    if dpkg -l | grep -q "qtmultimedia5-dev"; then
        log_check "PASS" "Qt5多媒体包" "qtmultimedia5-dev已安装"
    else
        log_check "FAIL" "Qt5多媒体包" "qtmultimedia5-dev未安装" "需要安装Qt5多媒体开发包"
    fi
    
    # 检查qmake
    if command -v qmake >/dev/null 2>&1; then
        local qmake_version=$(qmake -version | head -1)
        log_check "PASS" "qmake工具" "$qmake_version"
    else
        log_check "FAIL" "qmake工具" "qmake未找到" "需要安装Qt5开发工具"
    fi
}

# 检查编译工具链
check_build_tools() {
    echo -e "\n${BLUE}=== 检查编译工具链 ===${NC}"
    echo "=== 检查编译工具链 ===" >> "$REPORT_FILE"
    
    # 检查GCC
    if command -v gcc >/dev/null 2>&1; then
        local gcc_version=$(gcc --version | head -1)
        log_check "PASS" "GCC编译器" "$gcc_version"
    else
        log_check "FAIL" "GCC编译器" "gcc未安装" "需要安装build-essential"
    fi
    
    # 检查G++
    if command -v g++ >/dev/null 2>&1; then
        local gpp_version=$(g++ --version | head -1)
        log_check "PASS" "G++编译器" "$gpp_version"
    else
        log_check "FAIL" "G++编译器" "g++未安装" "需要安装build-essential"
    fi
    
    # 检查make
    if command -v make >/dev/null 2>&1; then
        local make_version=$(make --version | head -1)
        log_check "PASS" "Make工具" "$make_version"
    else
        log_check "FAIL" "Make工具" "make未安装" "需要安装build-essential"
    fi
    
    # 检查pkg-config
    if command -v pkg-config >/dev/null 2>&1; then
        local pkg_version=$(pkg-config --version)
        log_check "PASS" "pkg-config" "版本: $pkg_version"
    else
        log_check "FAIL" "pkg-config" "pkg-config未安装" "需要安装pkg-config"
    fi
}

# 检查多媒体库
check_multimedia() {
    echo -e "\n${BLUE}=== 检查多媒体库 ===${NC}"
    echo "=== 检查多媒体库 ===" >> "$REPORT_FILE"
    
    # 检查FFmpeg
    if command -v ffmpeg >/dev/null 2>&1; then
        local ffmpeg_version=$(ffmpeg -version 2>/dev/null | head -1 | cut -d' ' -f3)
        log_check "PASS" "FFmpeg" "版本: $ffmpeg_version"
    else
        log_check "FAIL" "FFmpeg" "ffmpeg未安装" "需要安装ffmpeg用于摄像头测试"
    fi
    
    # 检查v4l-utils
    if command -v v4l2-ctl >/dev/null 2>&1; then
        log_check "PASS" "V4L2工具" "v4l2-ctl可用"
    else
        log_check "WARN" "V4L2工具" "v4l2-ctl未安装" "建议安装v4l-utils用于摄像头管理"
    fi
}

# 检查目录结构
check_directories() {
    echo -e "\n${BLUE}=== 检查目录结构 ===${NC}"
    echo "=== 检查目录结构 ===" >> "$REPORT_FILE"
    
    # 检查/userdata目录
    if [ -d "/userdata" ]; then
        log_check "PASS" "/userdata目录" "目录存在"
        
        # 检查权限
        if [ -w "/userdata" ]; then
            log_check "PASS" "/userdata写权限" "具有写权限"
        else
            log_check "WARN" "/userdata写权限" "无写权限" "可能需要sudo权限"
        fi
    else
        log_check "WARN" "/userdata目录" "目录不存在" "可能需要创建/userdata目录"
    fi
    
    # 检查推理模型目录
    if [ -d "/userdata/install/demo_Linux_aarch64" ]; then
        log_check "PASS" "推理模型目录" "/userdata/install/demo_Linux_aarch64存在"
        
        # 检查demo可执行文件
        if [ -f "/userdata/install/demo_Linux_aarch64/demo" ]; then
            if [ -x "/userdata/install/demo_Linux_aarch64/demo" ]; then
                log_check "PASS" "推理程序" "demo可执行文件存在且可执行"
            else
                log_check "WARN" "推理程序" "demo文件存在但不可执行" "需要设置执行权限"
            fi
        else
            log_check "FAIL" "推理程序" "demo可执行文件不存在" "需要部署推理模型"
        fi
    else
        log_check "FAIL" "推理模型目录" "/userdata/install/demo_Linux_aarch64不存在" "需要部署推理模型"
    fi
}

# 检查摄像头设备
check_camera() {
    echo -e "\n${BLUE}=== 检查摄像头设备 ===${NC}"
    echo "=== 检查摄像头设备 ===" >> "$REPORT_FILE"
    
    local video_devices=$(ls /dev/video* 2>/dev/null | wc -l)
    if [ "$video_devices" -gt 0 ]; then
        log_check "PASS" "摄像头设备" "发现 $video_devices 个video设备"
        
        # 列出设备
        echo "  设备列表:" >> "$REPORT_FILE"
        ls /dev/video* 2>/dev/null | while read device; do
            echo "    $device" >> "$REPORT_FILE"
        done
    else
        log_check "WARN" "摄像头设备" "未发现video设备" "可能需要连接摄像头或加载驱动"
    fi
}

# 检查网络连接
check_network() {
    echo -e "\n${BLUE}=== 检查网络连接 ===${NC}"
    echo "=== 检查网络连接 ===" >> "$REPORT_FILE"
    
    if ping -c 1 8.8.8.8 >/dev/null 2>&1; then
        log_check "PASS" "网络连接" "网络连接正常"
    else
        log_check "WARN" "网络连接" "网络连接异常" "某些功能可能需要网络连接"
    fi
}

# 生成总结报告
generate_summary() {
    echo -e "\n${BLUE}=== 检查总结 ===${NC}"
    
    cat >> "$REPORT_FILE" << EOF
================================================================================
检查总结
================================================================================
总检查项目: $TOTAL_CHECKS
通过: $PASSED_CHECKS
失败: $FAILED_CHECKS  
警告: $WARNING_CHECKS

EOF

    if [ $FAILED_CHECKS -eq 0 ]; then
        echo -e "${GREEN}✓ 环境检查通过！系统已准备就绪。${NC}"
        echo "✓ 环境检查通过！系统已准备就绪。" >> "$REPORT_FILE"
    else
        echo -e "${RED}✗ 发现 $FAILED_CHECKS 个关键问题需要解决。${NC}"
        echo "✗ 发现 $FAILED_CHECKS 个关键问题需要解决。" >> "$REPORT_FILE"
    fi
    
    if [ $WARNING_CHECKS -gt 0 ]; then
        echo -e "${YELLOW}⚠ 有 $WARNING_CHECKS 个警告项目建议处理。${NC}"
        echo "⚠ 有 $WARNING_CHECKS 个警告项目建议处理。" >> "$REPORT_FILE"
    fi
    
    echo "" >> "$REPORT_FILE"
    echo "详细报告已保存到: $REPORT_FILE" >> "$REPORT_FILE"
    echo -e "\n详细报告已保存到: ${BLUE}$REPORT_FILE${NC}"
}

# 主函数
main() {
    echo -e "${BLUE}模型推理测试系统环境检查工具${NC}"
    echo "=================================="
    
    init_report
    check_architecture
    check_os
    check_qt5
    check_build_tools
    check_multimedia
    check_directories
    check_camera
    check_network
    generate_summary
    
    echo ""
    echo "请将生成的报告文件 '$REPORT_FILE' 发送给开发人员以获取相应的安装脚本。"
}

# 执行主函数
main "$@"
