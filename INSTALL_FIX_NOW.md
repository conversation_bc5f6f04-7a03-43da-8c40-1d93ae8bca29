# 立即修复安装问题

## 当前状况
prerm脚本导致安装和卸载都失败，现在提供多个解决方案。

## 方案1：使用空prerm脚本（推荐）

### 步骤1：构建新版本
```bash
cd test
./build_deb.sh --clean
```
这将生成 `diagnosis-app_1.0.4_arm64.deb`，包含空的prerm脚本。

### 步骤2：完全清理系统
```bash
# 强制清理
sudo ./debug_uninstall.sh cleanup

# 额外清理dpkg状态
sudo sed -i '/^Package: diagnosis-app$/,/^$/d' /var/lib/dpkg/status 2>/dev/null || true
sudo dpkg --configure -a
```

### 步骤3：安装新版本
```bash
sudo dpkg -i diagnosis-app_1.0.4_arm64.deb
```

## 方案2：跳过prerm脚本安装

如果方案1仍然失败：

```bash
# 使用强制选项跳过脚本错误
sudo dpkg -i --force-maintainer-script-errors diagnosis-app_1.0.4_arm64.deb

# 或者完全忽略脚本
sudo dpkg -i --force-all diagnosis-app_1.0.4_arm64.deb
```

## 方案3：手动安装

如果dpkg完全无法工作：

```bash
# 解压包到临时目录
mkdir -p /tmp/manual-install
dpkg-deb --extract diagnosis-app_1.0.4_arm64.deb /tmp/manual-install

# 手动复制文件
sudo cp -r /tmp/manual-install/opt /
sudo cp -r /tmp/manual-install/usr /

# 手动创建用户和组
sudo groupadd diagnosis 2>/dev/null || true
sudo useradd -r -g diagnosis -d /opt/diagnosis-app -s /bin/false diagnosis 2>/dev/null || true

# 设置权限
sudo chown -R diagnosis:diagnosis /opt/diagnosis-app
sudo chmod +x /opt/diagnosis-app/DiagnosisApp
sudo chmod +x /opt/diagnosis-app/run_diagnosis_app.sh

# 更新桌面数据库
sudo update-desktop-database 2>/dev/null || true

echo "手动安装完成"
```

## 方案4：诊断模式

运行诊断脚本了解具体问题：

```bash
./diagnose_install_issue.sh
```

这将：
1. 检查包文件完整性
2. 检查系统状态
3. 检查进程状态
4. 尝试详细安装并记录日志

## 方案5：创建最简包

如果所有方案都失败，创建一个没有任何脚本的包：

```bash
# 备份原脚本
cp debian/prerm debian/prerm.bak
cp debian/postrm debian/postrm.bak
cp debian/postinst debian/postinst.bak

# 创建空脚本
echo '#!/bin/bash' > debian/prerm
echo 'exit 0' >> debian/prerm

echo '#!/bin/bash' > debian/postrm
echo 'exit 0' >> debian/postrm

echo '#!/bin/bash' > debian/postinst
echo 'exit 0' >> debian/postinst

# 构建最简包
./build_deb.sh --clean

# 安装
sudo dpkg -i diagnosis-app_1.0.4_arm64.deb
```

## 测试安装成功

安装成功后验证：

```bash
# 检查文件
ls -la /opt/diagnosis-app/
ls -la /usr/local/bin/diagnosis-app
ls -la /usr/share/applications/diagnosis-app.desktop

# 检查用户
id diagnosis

# 测试运行
diagnosis-app --help 2>/dev/null || echo "命令行工具可能需要配置"
```

## 如果需要卸载

```bash
# 手动卸载
sudo rm -rf /opt/diagnosis-app
sudo rm -f /usr/local/bin/diagnosis-app
sudo rm -f /usr/share/applications/diagnosis-app.desktop
sudo userdel diagnosis 2>/dev/null || true
sudo groupdel diagnosis 2>/dev/null || true
sudo update-desktop-database 2>/dev/null || true

# 清理dpkg记录
sudo dpkg --remove --force-all diagnosis-app 2>/dev/null || true
```

## 建议执行顺序

1. **先尝试方案1**（空prerm脚本）
2. **如果失败，尝试方案2**（强制安装）
3. **如果还失败，运行方案4**（诊断）
4. **最后考虑方案3**（手动安装）

## 获取帮助

如果所有方案都失败，请提供：

```bash
# 收集系统信息
uname -a > system_info.txt
lsb_release -a >> system_info.txt
dpkg -l | grep diagnosis >> system_info.txt
ps aux | grep diagnosis >> system_info.txt
sudo journalctl -u dpkg --since "1 hour ago" > dpkg.log
```

然后分享这些文件以获得进一步帮助。
