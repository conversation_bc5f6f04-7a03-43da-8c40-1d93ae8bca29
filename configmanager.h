#ifndef CONFIGMANAGER_H
#define CONFIGMANAGER_H

#include <QObject>
#include <QSettings>
#include <QString>
#include <QStringList>
#include <QDebug>

/**
 * @brief 配置管理器类
 * 负责读取和管理应用程序的配置参数
 */
class ConfigManager : public QObject
{
    Q_OBJECT

public:
    static ConfigManager* instance();
    
    // 通用配置
    int getTimeoutSeconds() const;
    QString getTempDirectory() const;
    QString getLogLevel() const;
    QString getLogFile() const;
    
    // RK3588推理配置
    QString getRK3588DemoPath() const;
    QString getRK3588RknnModelPath() const;
    QString getRK3588RkllmModelPath() const;
    int getRK3588MaxTokens() const;
    int getRK3588ContextLength() const;
    int getRK3588CoreNum() const;
    // 自动中文输出方法已删除
    
    // 标准推理配置
    QString getStandardExecutablePath() const;
    
    // UI设置
    int getProgressUpdateInterval() const;
    bool getShowDetailedErrors() const;
    QString getDefaultInputPlaceholder() const;
    
    // 相机设置
    QString getCameraDetectionMode() const;
    QStringList getManualCameraDevices() const;
    int getCameraInitTimeout() const;
    int getImageSaveQuality() const;
    
    // 模型路径
    QString getModelPath(const QString& medicineType) const;
    QString getModelConfig(const QString& medicineType) const;
    
    // 重新加载配置
    void reloadConfig();

signals:
    void configChanged();
    void configError(const QString& error);

private:
    explicit ConfigManager(QObject *parent = nullptr);
    ~ConfigManager();
    
    void loadConfig();
    QString getConfigFilePath() const;
    
    static ConfigManager* m_instance;
    QSettings* m_settings;
    
    // 缓存的配置值
    mutable QHash<QString, QVariant> m_configCache;
};

#endif // CONFIGMANAGER_H
