# 部署模型推理测试系统配置文件
# 此文件包含图像推理过程中的各种参数设置
# 修改后需要重启应用程序才能生效

[General]
# 推理超时时间（秒）
timeout_seconds=300

# 临时文件目录
temp_directory=/tmp/inference-test-app

# 日志级别 (DEBUG, INFO, WARNING, ERROR)
log_level=INFO

# 日志文件路径
log_file=/var/log/inference-test-app/app.log

[RK3588_Inference]
# RK3588推理可执行文件路径
demo_path=/userdata/install/demo_Linux_aarch64/demo

# RKNN模型文件路径
rknn_model_path=/userdata/models/qwen2_vl_2b_vision_rk3588.rknn

# RKLLM模型文件路径
rkllm_model_path=/userdata/models/Qwen2-2B-vl-Instruct.rkllm

# 最大令牌数（增加以支持长文本输出）
max_tokens=256

# 上下文长度（增加以支持长文本输入）
context_length=1024

# CPU核心数
core_num=4

# 自动中文输出选项已删除 - 新demo自动处理

[Standard_Inference]
# 标准推理可执行文件路径
executable_path=./inference_engine

[UI_Settings]
# 进度更新间隔（毫秒）
progress_update_interval=100

# 是否显示详细错误信息
show_detailed_errors=true

# 默认输入提示
default_input_placeholder=请输入相关信息...

[Camera_Settings]
# 相机设备检测方式 (auto/manual)
camera_detection_mode=auto

# 手动指定相机设备路径（当detection_mode=manual时使用）
manual_camera_devices=/dev/video0,/dev/video1,/dev/video2

# 相机初始化超时时间（秒）
camera_init_timeout=10

# 图片保存质量 (1-100)
image_save_quality=85

[Model_Paths]
# 通用模型路径配置
model_a=models/model_a.rknn
model_b=models/model_b.rknn
model_c=models/model_c.rknn

[Model_Configs]
# 通用配置文件路径
config_a=config/config_a.json
config_b=config/config_b.json
config_c=config/config_c.json
