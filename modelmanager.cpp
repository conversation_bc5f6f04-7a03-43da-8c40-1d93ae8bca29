#include "modelmanager.h"
#include <QDebug>
#include <QFile>
#include <QRegExp>
#include <QMutexLocker>

ModelManager::ModelManager(QObject *parent)
    : QObject(parent)
    , m_demoProcess(nullptr)
    , m_initTimer(nullptr)
    , m_isModelLoaded(false)
    , m_isInitializing(false)
    , m_restartAttempts(0)
    , m_restartTimer(nullptr)
    , m_inferenceTimer(nullptr)
{
    // 从配置文件读取参数
    ConfigManager* config = ConfigManager::instance();
    m_demoPath = config->getRK3588DemoPath();
    m_rknnModelPath = config->getRK3588RknnModelPath();
    m_rkllmModelPath = config->getRK3588RkllmModelPath();
    m_maxTokens = config->getRK3588MaxTokens();
    m_contextLength = config->getRK3588ContextLength();
    m_coreNum = config->getRK3588CoreNum();
    
    qDebug() << "ModelManager initialized with config:";
    qDebug() << "  Demo路径:" << m_demoPath;
    qDebug() << "  RKNN模型:" << m_rknnModelPath;
    qDebug() << "  RKLLM模型:" << m_rkllmModelPath;
}

ModelManager::~ModelManager()
{
    qDebug() << "ModelManager析构开始";

    // 断开所有信号连接
    this->disconnect();

    // 停止模型
    stopModel();

    qDebug() << "ModelManager析构完成";
}

bool ModelManager::initializeModel()
{
    qDebug() << "ModelManager::initializeModel() 开始";

    if (m_isModelLoaded || m_isInitializing) {
        qDebug() << "模型已加载或正在初始化中，跳过重复初始化";
        qDebug() << "  m_isModelLoaded:" << m_isModelLoaded;
        qDebug() << "  m_isInitializing:" << m_isInitializing;
        return m_isModelLoaded;
    }
    
    // 检查demo程序是否存在
    if (!QFile::exists(m_demoPath)) {
        QString error = QString("Demo程序不存在: %1").arg(m_demoPath);
        qWarning() << error;
        emit modelLoadFailed(error);
        return false;
    }
    
    // 检查模型文件是否存在
    if (!QFile::exists(m_rknnModelPath)) {
        QString error = QString("RKNN模型文件不存在: %1").arg(m_rknnModelPath);
        qWarning() << error;
        emit modelLoadFailed(error);
        return false;
    }
    
    if (!QFile::exists(m_rkllmModelPath)) {
        QString error = QString("RKLLM模型文件不存在: %1").arg(m_rkllmModelPath);
        qWarning() << error;
        emit modelLoadFailed(error);
        return false;
    }
    
    m_isInitializing = true;
    setupProcess();
    
    // 构建启动命令参数
    QStringList arguments;
    arguments << m_rknnModelPath << m_rkllmModelPath 
              << QString::number(m_maxTokens) << QString::number(m_contextLength) 
              << QString::number(m_coreNum);
    
    qDebug() << "启动模型加载进程:" << m_demoPath << arguments;
    
    // 启动进程
    m_demoProcess->setProgram(m_demoPath);
    m_demoProcess->setArguments(arguments);
    
    // 启动初始化超时定时器
    m_initTimer->start(INIT_TIMEOUT_MS);
    
    m_demoProcess->start();
    
    if (!m_demoProcess->waitForStarted(5000)) {
        QString error = "Demo进程启动失败";
        qWarning() << error;
        m_isInitializing = false;
        m_initTimer->stop();
        emit modelLoadFailed(error);
        return false;
    }
    
    qDebug() << "Demo进程已启动，等待模型加载完成...";
    return true;
}

void ModelManager::stopModel()
{
    QMutexLocker locker(&m_processMutex);
    
    if (m_demoProcess && m_demoProcess->state() != QProcess::NotRunning) {
        qDebug() << "发送exit命令停止模型";
        
        // 发送exit命令
        m_demoProcess->write("exit\n");
        m_demoProcess->waitForBytesWritten(1000);
        
        // 等待进程正常退出
        if (!m_demoProcess->waitForFinished(5000)) {
            qDebug() << "强制终止模型进程";
            m_demoProcess->kill();
            m_demoProcess->waitForFinished(3000);
        }
    }
    
    cleanupProcess();
    m_isModelLoaded = false;
    m_isInitializing = false;
    emit processStatusChanged(false);
}

void ModelManager::sendInferenceRequest(const QString& imagePath, const QString& questionText)
{
    qDebug() << "开始推理请求:" << imagePath;

    QMutexLocker locker(&m_processMutex);

    if (!m_isModelLoaded || !m_demoProcess || m_demoProcess->state() != QProcess::Running) {
        QString errorMsg = "模型未加载或进程未运行";
        qWarning() << errorMsg;
        emit inferenceError(errorMsg);
        return;
    }
    
    // 构建推理请求：图片路径 + 空格 + 文本（包含<image>标签）
    // 确保图片路径不被引号包围，文本内容正确格式化
    // 重要：将文本中的换行符替换为\\n，避免被demo程序解释为命令分隔符
    QString processedText = questionText;
    processedText.replace("\n", "\\n");  // 将真实换行符替换为字面量\n

    QString request = imagePath + " " + processedText + "\n";

    qDebug() << "发送推理请求:" << imagePath;
    
    // 清空累积输出，准备接收新的推理结果
    m_accumulatedOutput.clear();

    // 启动推理超时定时器
    if (!m_inferenceTimer) {
        m_inferenceTimer = new QTimer(this);
        m_inferenceTimer->setSingleShot(true);
        connect(m_inferenceTimer, &QTimer::timeout, [this]() {
            qWarning() << "推理请求超时，没有收到响应";
            emit inferenceError("推理超时：30秒内没有收到模型响应");
        });
    }
    m_inferenceTimer->start(INFERENCE_TIMEOUT_MS);
    
    // 发送请求
    QByteArray requestBytes = request.toUtf8();
    m_demoProcess->write(requestBytes);
    bool writeSuccess = m_demoProcess->waitForBytesWritten(3000);

    if (!writeSuccess) {
        qWarning() << "发送推理请求失败:" << m_demoProcess->errorString();
        emit inferenceError("发送推理请求失败: " + m_demoProcess->errorString());
        return;
    }

    qDebug() << "推理请求已发送，等待响应...";




}

bool ModelManager::isProcessRunning() const
{
    return m_demoProcess && m_demoProcess->state() == QProcess::Running;
}

void ModelManager::setupProcess()
{
    if (m_demoProcess) {
        cleanupProcess();
    }
    
    m_demoProcess = new QProcess(this);
    
    // 连接信号
    connect(m_demoProcess, QOverload<int, QProcess::ExitStatus>::of(&QProcess::finished),
            this, &ModelManager::onProcessFinished);
    connect(m_demoProcess, &QProcess::errorOccurred,
            this, &ModelManager::onProcessError);
    connect(m_demoProcess, &QProcess::readyReadStandardOutput,
            this, &ModelManager::onProcessReadyRead);
    
    // 设置初始化超时定时器
    if (!m_initTimer) {
        m_initTimer = new QTimer(this);
        m_initTimer->setSingleShot(true);
        connect(m_initTimer, &QTimer::timeout, this, &ModelManager::onInitTimeout);
    }
    
    m_accumulatedOutput.clear();
}

void ModelManager::cleanupProcess()
{
    if (m_initTimer) {
        m_initTimer->stop();
    }

    if (m_restartTimer) {
        m_restartTimer->stop();
    }

    if (m_demoProcess) {
        m_demoProcess->disconnect();
        if (m_demoProcess->state() != QProcess::NotRunning) {
            m_demoProcess->kill();
            m_demoProcess->waitForFinished(3000);
        }
        m_demoProcess->deleteLater();
        m_demoProcess = nullptr;
    }
}

void ModelManager::onProcessFinished(int exitCode, QProcess::ExitStatus exitStatus)
{
    qDebug() << "Demo进程结束, exitCode:" << exitCode << "exitStatus:" << exitStatus;

    m_isModelLoaded = false;
    m_isInitializing = false;

    if (m_initTimer) {
        m_initTimer->stop();
    }

    emit processStatusChanged(false);

    if (exitCode != 0 || exitStatus == QProcess::CrashExit) {
        QString error = QString("Demo进程异常退出，退出码: %1").arg(exitCode);
        qWarning() << error;

        // 尝试重启
        if (m_restartAttempts < MAX_RESTART_ATTEMPTS) {
            m_restartAttempts++;
            qDebug() << QString("尝试重启demo进程 (第%1次，最多%2次)").arg(m_restartAttempts).arg(MAX_RESTART_ATTEMPTS);
            attemptRestart();
        } else {
            qWarning() << "已达到最大重启次数，停止重启尝试";
            emit modelLoadFailed(error + QString(" (已重试%1次)").arg(MAX_RESTART_ATTEMPTS));
        }
    } else {
        // 正常退出，重置重启计数
        m_restartAttempts = 0;
    }
}

void ModelManager::onProcessError(QProcess::ProcessError error)
{
    QString errorMsg;
    switch (error) {
    case QProcess::FailedToStart:
        errorMsg = "Demo进程启动失败";
        break;
    case QProcess::Crashed:
        errorMsg = "Demo进程崩溃";
        break;
    case QProcess::Timedout:
        errorMsg = "Demo进程超时";
        break;
    default:
        errorMsg = "Demo进程未知错误";
        break;
    }
    
    qWarning() << "进程错误:" << errorMsg;
    
    m_isModelLoaded = false;
    m_isInitializing = false;
    
    if (m_initTimer) {
        m_initTimer->stop();
    }
    
    emit processStatusChanged(false);
    emit modelLoadFailed(errorMsg);
}

void ModelManager::onProcessReadyRead()
{
    if (!m_demoProcess) return;

    QByteArray data = m_demoProcess->readAllStandardOutput();
    QString output = QString::fromUtf8(data);

    if (!output.isEmpty()) {
        m_accumulatedOutput += output;
        parseProcessOutput(output);
    }

    // 检查stderr
    QByteArray errorData = m_demoProcess->readAllStandardError();
    if (!errorData.isEmpty()) {
        QString errorOutput = QString::fromUtf8(errorData);
        qWarning() << "进程错误:" << errorOutput;
    }
}

void ModelManager::onInitTimeout()
{
    qWarning() << "模型初始化超时";
    m_isInitializing = false;
    emit modelLoadFailed("模型初始化超时");
    stopModel();
}

void ModelManager::parseProcessOutput(const QString& output)
{

    // 检查是否模型加载完成
    if (m_isInitializing && (output.contains("请选择") || output.contains("输入") || output.contains("user:"))) {
        qDebug() << "模型加载完成";
        m_isInitializing = false;
        m_isModelLoaded = true;
        m_restartAttempts = 0;

        if (m_initTimer) {
            m_initTimer->stop();
        }

        emit processStatusChanged(true);
        emit modelLoaded();
        return;
    }
    
    // 如果模型已加载，解析推理结果
    if (m_isModelLoaded) {
        // 检查是否包含错误信息
        if (output.contains("Error:") || output.contains("Cannot load image")) {
            qWarning() << "检测到错误信息:" << output;
        }

        // 检查是否包含完整的robot回答（包含"答案："和结束标志）
        if (m_accumulatedOutput.contains("robot:") &&
            m_accumulatedOutput.contains("答案：") &&
            (m_accumulatedOutput.contains("user:") || output.contains("user:"))) {

            // 提取robot回答内容
            QString robotContent = "";

            // 直接查找robot:到user:之间的内容
            int robotStart = m_accumulatedOutput.indexOf("robot:");
            int userEnd = m_accumulatedOutput.lastIndexOf("user:");

            if (robotStart != -1 && userEnd > robotStart) {
                robotContent = m_accumulatedOutput.mid(robotStart + 6, userEnd - robotStart - 6).trimmed();

                if (!robotContent.isEmpty()) {
                    qDebug() << "推理完成";

                    // 停止推理超时定时器
                    if (m_inferenceTimer) {
                        m_inferenceTimer->stop();
                    }

                    emit inferenceCompleted(robotContent);
                    m_accumulatedOutput.clear(); // 清空累积输出
                    return; // 处理完成，退出
                }
            }
        }
    }
}

void ModelManager::attemptRestart()
{
    qDebug() << "准备重启demo进程...";

    // 清理当前进程
    cleanupProcess();

    // 创建重启定时器
    if (!m_restartTimer) {
        m_restartTimer = new QTimer(this);
        m_restartTimer->setSingleShot(true);
        connect(m_restartTimer, &QTimer::timeout, [this]() {
            qDebug() << "开始重启demo进程";
            if (!initializeModel()) {
                qWarning() << "重启失败";
                if (m_restartAttempts >= MAX_RESTART_ATTEMPTS) {
                    emit modelLoadFailed("重启失败，已达到最大重试次数");
                }
            }
        });
    }

    // 延迟重启
    qDebug() << QString("将在%1毫秒后重启").arg(RESTART_DELAY_MS);
    m_restartTimer->start(RESTART_DELAY_MS);
}
