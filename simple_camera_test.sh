#!/bin/bash
# 简单摄像头测试脚本 - 测试所有摄像头设备

TEST_DIR="camera_test_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$TEST_DIR"

echo "=== 摄像头测试 ==="
echo "测试目录: $TEST_DIR"
echo ""

# 发现所有video设备
all_devices=($(ls /dev/video* 2>/dev/null | sort -V))

echo "发现 ${#all_devices[@]} 个设备，开始测试..."
echo ""

working_devices=()

for device in "${all_devices[@]}"; do
    if [ -c "$device" ]; then
        echo "测试: $device"
        device_name=$(basename "$device")
        image_file="$TEST_DIR/${device_name}.jpg"

        if sudo ffmpeg -f v4l2 -video_size 640x480 -i "$device" -vframes 1 -y "$image_file" >/dev/null 2>&1; then
            if [ -f "$image_file" ] && [ -s "$image_file" ]; then
                echo "  ✓ 成功: $image_file"
                sudo chown $USER:$USER "$image_file" 2>/dev/null
                working_devices+=("$device")
            else
                echo "  ✗ 失败"
            fi
        else
            echo "  ✗ 失败"
        fi
    fi
done

echo ""
echo "=== 结果 ==="
echo "可用设备: ${#working_devices[@]}"
if [ ${#working_devices[@]} -gt 0 ]; then
    for device in "${working_devices[@]}"; do
        echo "  $device"
    done
    echo ""
    echo "图片保存在: $TEST_DIR/"
else
    echo "没有发现可用设备"
fi
