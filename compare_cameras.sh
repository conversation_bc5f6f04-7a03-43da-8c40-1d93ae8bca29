#!/bin/bash
# 比较video0、video2、video4摄像头的详细信息

TEST_DIR="camera_compare_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$TEST_DIR"

echo "=== 摄像头详细比较分析 ==="
echo "比较设备: /dev/video0, /dev/video2, /dev/video4"
echo "测试目录: $TEST_DIR"
echo ""

devices=("/dev/video0" "/dev/video2" "/dev/video4")

for device in "${devices[@]}"; do
    echo "========================================"
    echo "分析设备: $device"
    echo "========================================"
    
    # 1. 基本设备信息
    echo "1. 基本信息:"
    echo "  设备文件: $device"
    echo "  权限: $(ls -l $device)"
    echo ""
    
    # 2. V4L2设备详细信息
    if command -v v4l2-ctl >/dev/null 2>&1; then
        echo "2. V4L2设备信息:"
        sudo v4l2-ctl -d "$device" --info 2>/dev/null | sed 's/^/  /' || echo "  无法获取设备信息"
        echo ""
        
        echo "3. 设备能力:"
        sudo v4l2-ctl -d "$device" --list-formats-ext 2>/dev/null | head -20 | sed 's/^/  /' || echo "  无法获取格式信息"
        echo ""
        
        echo "4. 当前设置:"
        sudo v4l2-ctl -d "$device" --get-fmt-video 2>/dev/null | sed 's/^/  /' || echo "  无法获取当前格式"
        echo ""
        
        echo "5. 控制选项:"
        sudo v4l2-ctl -d "$device" --list-ctrls 2>/dev/null | head -10 | sed 's/^/  /' || echo "  无法获取控制选项"
        echo ""
    fi
    
    # 3. 拍摄测试图片
    echo "6. 拍摄测试:"
    device_name=$(basename "$device")
    image_file="$TEST_DIR/${device_name}_test.jpg"
    
    echo "  拍摄640x480图片..."
    if sudo ffmpeg -f v4l2 -video_size 640x480 -i "$device" -vframes 1 -y "$image_file" >/dev/null 2>&1; then
        if [ -f "$image_file" ] && [ -s "$image_file" ]; then
            echo "  ✓ 拍摄成功: $image_file"
            sudo chown $USER:$USER "$image_file" 2>/dev/null
            
            # 获取图片详细信息
            if command -v identify >/dev/null 2>&1; then
                echo "  图片信息:"
                identify "$image_file" 2>/dev/null | sed 's/^/    /' || echo "    无法获取图片信息"
            fi
            
            # 文件大小
            file_size=$(ls -lh "$image_file" | awk '{print $5}')
            echo "  文件大小: $file_size"
        else
            echo "  ✗ 拍摄失败"
        fi
    else
        echo "  ✗ 拍摄失败"
    fi
    echo ""
    
    # 4. 测试不同分辨率支持
    echo "7. 分辨率支持测试:"
    resolutions=("320x240" "640x480" "1280x720" "1920x1080")
    for res in "${resolutions[@]}"; do
        echo -n "  $res: "
        if timeout 3 sudo ffmpeg -f v4l2 -video_size "$res" -i "$device" -vframes 1 -f null - >/dev/null 2>&1; then
            echo "✓ 支持"
        else
            echo "✗ 不支持"
        fi
    done
    echo ""
    
    # 5. 测试不同像素格式
    echo "8. 像素格式测试:"
    formats=("yuyv422" "mjpeg" "h264")
    for fmt in "${formats[@]}"; do
        echo -n "  $fmt: "
        if timeout 3 sudo ffmpeg -f v4l2 -video_size 640x480 -pixel_format "$fmt" -i "$device" -vframes 1 -f null - >/dev/null 2>&1; then
            echo "✓ 支持"
        else
            echo "✗ 不支持"
        fi
    done
    echo ""
    echo ""
done

# 生成对比总结
echo "========================================"
echo "=== 对比总结 ==="
echo "========================================"

echo "测试图片已保存在: $TEST_DIR/"
echo ""
echo "请检查以下内容来判断区别:"
echo ""
echo "1. 📷 图片内容对比:"
echo "   - 查看 video0_test.jpg"
echo "   - 查看 video2_test.jpg" 
echo "   - 查看 video4_test.jpg"
echo "   - 确认哪些拍摄的是相同/不同的物理摄像头"
echo ""
echo "2. 🔍 技术规格对比:"
echo "   - 查看上面的V4L2设备信息"
echo "   - 比较支持的分辨率和格式"
echo "   - 查看驱动程序名称"
echo ""
echo "3. 💡 可能的区别:"
echo "   - video0: 可能是主摄像头或原始数据流"
echo "   - video2: 可能是处理后的数据流或不同模式"
echo "   - video4: 可能是另一个物理摄像头位置"
echo ""
echo "4. 🎯 选择建议:"
echo "   - 如果video4是您想要的摄像头，程序应该优先使用它"
echo "   - 可以在程序中设置摄像头优先级: video4 > video0 > video2"

# 如果有exiftool，显示图片的EXIF信息
if command -v exiftool >/dev/null 2>&1; then
    echo ""
    echo "5. 📊 图片EXIF信息对比:"
    for device in "${devices[@]}"; do
        device_name=$(basename "$device")
        image_file="$TEST_DIR/${device_name}_test.jpg"
        if [ -f "$image_file" ]; then
            echo ""
            echo "  $device_name:"
            exiftool "$image_file" 2>/dev/null | grep -E "(Image Size|Camera|Make|Model)" | sed 's/^/    /' || echo "    无EXIF信息"
        fi
    done
fi

echo ""
echo "请查看拍摄的图片，然后告诉我:"
echo "- video0、video2、video4拍摄的内容有什么不同？"
echo "- video4为什么是您想要的摄像头？"
echo "- 是否需要在程序中优先使用video4？"
