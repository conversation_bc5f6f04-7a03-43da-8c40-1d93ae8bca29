#!/bin/bash
# 测试修改是否正确的脚本

echo "=== 测试修改结果 ==="

echo "1. 检查启动脚本重命名:"
if [ -f "build/run_inference_app.sh" ]; then
    echo "  ✓ 新启动脚本存在: build/run_inference_app.sh"
else
    echo "  ✗ 新启动脚本不存在: build/run_inference_app.sh"
fi

if [ -f "build/run_diagnosis_app.sh" ]; then
    echo "  ! 旧启动脚本仍存在: build/run_diagnosis_app.sh"
else
    echo "  ✓ 旧启动脚本已清理"
fi

echo ""
echo "2. 检查taskpage.cpp中的修改:"
if grep -q "ConfigManager\* config = ConfigManager::instance();" taskpage.cpp; then
    echo "  ✓ taskpage.cpp已使用ConfigManager"
else
    echo "  ✗ taskpage.cpp未使用ConfigManager"
fi

if grep -q "questionText.*<image>" taskpage.cpp; then
    echo "  ✓ taskpage.cpp已添加<image>标签"
else
    echo "  ✗ taskpage.cpp未添加<image>标签"
fi

echo ""
echo "3. 检查调试信息清理:"
debug_count=$(grep -c "\[调试\]" taskpage.cpp || echo "0")
echo "  剩余调试信息数量: $debug_count"

if [ "$debug_count" -lt 5 ]; then
    echo "  ✓ 大部分调试信息已清理"
else
    echo "  ! 仍有较多调试信息"
fi

echo ""
echo "4. 检查配置文件引用:"
if grep -q "getRK3588DemoPath" taskpage.cpp; then
    echo "  ✓ taskpage.cpp使用配置文件路径"
else
    echo "  ✗ taskpage.cpp未使用配置文件路径"
fi

echo ""
echo "=== 测试完成 ==="
