#!/bin/bash
# 安装后脚本

set -e

# 设置应用程序目录权限
chmod -R 755 /opt/inference-test-app
chmod +x /opt/inference-test-app/InferenceApp
chmod +x /opt/inference-test-app/run_inference_app.sh

# 确保图片保存目录存在并设置权限
mkdir -p /opt/inference-test-app/saved_images
chmod 755 /opt/inference-test-app/saved_images

# 确保日志目录存在并设置权限
mkdir -p /var/log/inference-test-app
chmod 755 /var/log/inference-test-app
chown root:root /var/log/inference-test-app

# 创建配置目录（如果不存在）
mkdir -p /etc/inference-test-app
if [ ! -f /etc/inference-test-app/inference.conf ]; then
    cp /opt/inference-test-app/config/inference.conf.example /etc/inference-test-app/inference.conf
fi

# 创建日志目录
mkdir -p /var/log/inference-test-app
chown -R $SUDO_USER:$SUDO_USER /var/log/inference-test-app 2>/dev/null || true

# 创建桌面快捷方式（如果有桌面环境）
if [ -d "/usr/share/applications" ]; then
    cat > /usr/share/applications/inference-test-app.desktop << EOF
[Desktop Entry]
Version=1.0
Type=Application
Name=模型推理测试系统
Name[en]=Inference Test App
Comment=部署模型推理测试系统
Comment[en]=Model inference testing system
Exec=/opt/inference-test-app/run_inference_app.sh
Icon=/opt/inference-test-app/icons/app-icon.png
Terminal=false
Categories=Utility;Development;
EOF
    chmod 644 /usr/share/applications/inference-test-app.desktop
fi

# 创建命令行快捷方式
if [ -d "/usr/local/bin" ]; then
    cat > /usr/local/bin/inference-test-app << 'EOF'
#!/bin/bash
# 模型推理测试系统命令行启动器
cd /opt/inference-test-app
exec ./run_inference_app.sh "$@"
EOF
    chmod +x /usr/local/bin/inference-test-app
fi

# 设置摄像头权限
echo "配置摄像头权限..."
# 确保video组存在
groupadd -f video 2>/dev/null || true

# 将当前用户添加到video组（如果是通过sudo安装）
if [ -n "$SUDO_USER" ]; then
    usermod -a -G video "$SUDO_USER" 2>/dev/null || true
    echo "已将用户 $SUDO_USER 添加到 video 组"
fi

# 设置摄像头设备权限
if [ -e "/dev/video0" ]; then
    chmod 666 /dev/video0 2>/dev/null || true
fi

echo "模型推理测试系统安装完成！"
echo "使用方法："
echo "1. 命令行运行: sudo inference-test-app"
echo "2. 直接运行: /opt/inference-test-app/run_inference_app.sh"
echo "3. 配置文件: /etc/inference-test-app/inference.conf"
echo ""
echo "摄像头使用说明："
echo "- 如果摄像头权限有问题，请运行: /opt/inference-test-app/fix_camera_permissions.sh"
echo "- 或手动设置: sudo usermod -a -G video \$USER && newgrp video"
echo "- 临时权限: sudo chmod 666 /dev/video*"

exit 0
