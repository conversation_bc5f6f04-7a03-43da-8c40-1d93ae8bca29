#!/bin/bash
# 配置文件验证脚本

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
echo_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
echo_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
echo_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 配置文件路径
CONFIG_PATHS=(
    "/etc/inference-test-app/inference.conf"
    "./config/inference.conf"
    "./config/inference.conf.example"
)

# 查找配置文件
find_config_file() {
    for config_path in "${CONFIG_PATHS[@]}"; do
        if [[ -f "$config_path" ]]; then
            echo "$config_path"
            return 0
        fi
    done
    return 1
}

# 验证配置文件
validate_config() {
    local config_file="$1"
    local errors=0
    
    echo_info "验证配置文件: $config_file"
    
    # 检查文件是否可读
    if [[ ! -r "$config_file" ]]; then
        echo_error "配置文件不可读: $config_file"
        return 1
    fi
    
    # 检查必需的节
    local required_sections=("General" "RK3588_Inference" "Standard_Inference" "Camera_Settings")
    for section in "${required_sections[@]}"; do
        if ! grep -q "^\[$section\]" "$config_file"; then
            echo_warning "缺少配置节: [$section]"
            ((errors++))
        else
            echo_success "找到配置节: [$section]"
        fi
    done
    
    # 检查关键配置项
    local key_configs=(
        "General:timeout_seconds"
        "RK3588_Inference:demo_path"
        "RK3588_Inference:rknn_model_path"
        "RK3588_Inference:rkllm_model_path"
        "RK3588_Inference:max_tokens"
        "RK3588_Inference:context_length"
    )
    
    for config in "${key_configs[@]}"; do
        local section="${config%:*}"
        local key="${config#*:}"
        
        if grep -A 20 "^\[$section\]" "$config_file" | grep -q "^$key="; then
            local value=$(grep -A 20 "^\[$section\]" "$config_file" | grep "^$key=" | cut -d'=' -f2)
            echo_success "[$section] $key = $value"
        else
            echo_warning "缺少配置项: [$section] $key"
            ((errors++))
        fi
    done
    
    return $errors
}

# 检查模型文件
check_model_files() {
    local config_file="$1"
    
    echo_info "检查模型文件..."
    
    # 提取模型路径
    local rknn_path=$(grep -A 20 "^\[RK3588_Inference\]" "$config_file" | grep "^rknn_model_path=" | cut -d'=' -f2)
    local rkllm_path=$(grep -A 20 "^\[RK3588_Inference\]" "$config_file" | grep "^rkllm_model_path=" | cut -d'=' -f2)
    local demo_path=$(grep -A 20 "^\[RK3588_Inference\]" "$config_file" | grep "^demo_path=" | cut -d'=' -f2)
    
    # 检查文件是否存在
    if [[ -n "$rknn_path" ]]; then
        if [[ -f "$rknn_path" ]]; then
            echo_success "RKNN模型文件存在: $rknn_path"
        else
            echo_warning "RKNN模型文件不存在: $rknn_path"
        fi
    fi
    
    if [[ -n "$rkllm_path" ]]; then
        if [[ -f "$rkllm_path" ]]; then
            echo_success "RKLLM模型文件存在: $rkllm_path"
        else
            echo_warning "RKLLM模型文件不存在: $rkllm_path"
        fi
    fi
    
    if [[ -n "$demo_path" ]]; then
        if [[ -f "$demo_path" ]]; then
            echo_success "Demo可执行文件存在: $demo_path"
        else
            echo_warning "Demo可执行文件不存在: $demo_path"
        fi
    fi
}

# 生成示例配置
generate_example_config() {
    local output_file="$1"
    
    echo_info "生成示例配置文件: $output_file"
    
    cat > "$output_file" << 'EOF'
# 模型推理测试系统配置文件示例
# 复制此文件到 /etc/inference-test-app/inference.conf 并根据需要修改

[General]
timeout_seconds=300
temp_directory=/tmp/inference-test-app
log_level=INFO
log_file=/var/log/inference-test-app/app.log

[RK3588_Inference]
demo_path=/userdata/install/demo_Linux_aarch64/demo
rknn_model_path=/userdata/models/qwen2_vl_2b_vision_rk3588.rknn
rkllm_model_path=/userdata/models/Qwen2-2B-vl-Instruct.rkllm
max_tokens=128
context_length=512
core_num=4
auto_chinese_output=true

[Standard_Inference]
executable_path=./inference_engine
enable_mock_result=true
mock_result_delay=2000

[UI_Settings]
progress_update_interval=100
show_detailed_errors=true
default_symptom_placeholder=请详细描述您的症状...

[Camera_Settings]
camera_detection_mode=auto
manual_camera_devices=/dev/video0,/dev/video1,/dev/video2
camera_init_timeout=10
image_save_quality=85

[Model_Paths]
cold_medicine_model=models/cold_medicine_model.rknn
painkiller_model=models/painkiller_model.rknn
antiinflammatory_model=models/antiinflammatory_model.rknn
digestive_model=models/digestive_model.rknn
vitamin_model=models/vitamin_model.rknn

[Model_Configs]
cold_medicine_config=config/cold_medicine_config.json
painkiller_config=config/painkiller_config.json
antiinflammatory_config=config/antiinflammatory_config.json
digestive_config=config/digestive_config.json
vitamin_config=config/vitamin_config.json
EOF

    echo_success "示例配置文件已生成: $output_file"
}

# 主函数
main() {
    echo_info "=== 诊断推理应用配置验证工具 ==="
    
    # 查找配置文件
    config_file=$(find_config_file)
    if [[ $? -ne 0 ]]; then
        echo_warning "未找到配置文件，生成示例配置..."
        generate_example_config "inference.conf.example"
        echo_info "请将示例配置文件复制到适当位置并修改"
        return 1
    fi
    
    echo_info "使用配置文件: $config_file"
    
    # 验证配置
    if validate_config "$config_file"; then
        echo_success "配置文件验证通过"
    else
        echo_warning "配置文件存在问题，但可能仍然可用"
    fi
    
    # 检查模型文件
    check_model_files "$config_file"
    
    echo_info "=== 验证完成 ==="
}

# 处理命令行参数
case "${1:-}" in
    --generate-example)
        generate_example_config "inference.conf.example"
        ;;
    --help|-h)
        echo "用法: $0 [选项]"
        echo "选项:"
        echo "  --generate-example  生成示例配置文件"
        echo "  --help, -h         显示此帮助信息"
        ;;
    *)
        main "$@"
        ;;
esac
