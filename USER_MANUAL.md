# 模型推理测试系统 - 用户操作手册

## 📋 目录
- [系统要求](#系统要求)
- [下载软件](#下载软件)
- [安装步骤](#安装步骤)
- [使用指南](#使用指南)
- [常见问题](#常见问题)
- [卸载方法](#卸载方法)
- [技术支持](#技术支持)

---

## 🖥️ 系统要求

### 硬件要求
- **处理器**: ARM64架构 (aarch64)
- **内存**: 至少2GB RAM
- **存储**: 至少1GB可用空间
- **摄像头**: USB摄像头或内置摄像头

### 软件要求
- **操作系统**: Debian 10+ 或 Ubuntu 18.04+
- **架构**: arm64 (aarch64)
- **权限**: 需要管理员权限进行安装

---

## 📥 下载软件

### 获取安装包
1. 从提供方获取安装包文件：`inference-test-app_1.0.0_arm64.deb`
2. 将文件传输到目标设备：
   ```bash
   # 方法1: 使用U盘
   # 将.deb文件复制到U盘，然后插入目标设备
   
   # 方法2: 使用网络传输（如果有网络）
   scp inference-test-app_1.0.0_arm64.deb user@target-device:/home/<USER>/
   
   # 方法3: 使用其他传输方式
   # 根据实际情况选择合适的文件传输方法
   ```

### 验证文件完整性
```bash
# 检查文件是否完整
ls -lh inference-test-app_1.0.0_arm64.deb
file inference-test-app_1.0.0_arm64.deb
```

---

## 🔧 安装步骤

### 步骤1: 准备安装环境
```bash
# 更新软件包列表
sudo apt update

# 安装必要的依赖（如果尚未安装）
sudo apt install -y qtbase5-dev qtmultimedia5-dev libqt5multimedia5-plugins
```

### 步骤2: 安装软件包
```bash
# 安装.deb包
sudo dpkg -i inference-test-app_1.0.0_arm64.deb

# 如果出现依赖问题，运行以下命令修复
sudo apt-get install -f
```

### 步骤3: 验证安装
```bash
# 检查安装是否成功
dpkg -l | grep inference-test-app

# 检查文件是否正确安装
ls -la /opt/inference-test-app/
```

### 步骤4: 配置摄像头权限
```bash
# 将当前用户添加到video组
sudo usermod -a -G video $USER

# 重新登录或运行以下命令使权限生效
newgrp video

# 或者重启系统
sudo reboot
```

---

## 🚀 使用指南

### 启动方法

#### 方法1: 命令行启动
```bash
# 直接运行命令
inference-test-app

# 或者使用完整路径
/opt/inference-test-app/run_inference_app.sh
```

#### 方法2: 桌面启动
1. 在应用程序菜单中找到"模型推理测试系统"
2. 点击图标启动

#### 方法3: 文件管理器启动
1. 打开文件管理器
2. 导航到 `/opt/inference-test-app/`
3. 双击 `run_inference_app.sh`

### 基本操作

#### 1. 拍摄图片
1. 启动软件后，程序会自动检测摄像头
2. 点击"拍照"按钮进行拍摄
3. 图片会自动保存到 `/opt/inference-test-app/saved_images/`

#### 2. 进行推理测试
1. 拍摄或选择图片后
2. 输入相关问题或描述
3. 点击"开始推理"按钮
4. 等待推理结果显示

#### 3. 查看结果
- **分析结果**: 显示模型的分析过程
- **答案**: 显示推理得出的答案
- **标准答案**: 显示预期的正确答案（如果有）

### 配置文件

#### 主配置文件位置
```
/etc/inference-test-app/inference.conf
```

#### 常用配置项
```ini
[RK3588_Inference]
# 推理程序路径
demo_path=/userdata/install/demo_Linux_aarch64/demo

# 模型文件路径
rknn_model_path=/userdata/models/model.rknn
rkllm_model_path=/userdata/models/model.rkllm

[UI_Settings]
# 默认输入提示
default_input_placeholder=请输入相关信息...
```

---

## ❓ 常见问题

### Q1: 摄像头无法使用
**解决方法**:
```bash
# 检查摄像头设备
ls /dev/video*

# 检查权限
groups | grep video

# 修复权限
sudo /opt/inference-test-app/fix_camera_permissions.sh

# 手动设置权限
sudo chmod 666 /dev/video*
```

### Q2: 程序无法启动
**解决方法**:
```bash
# 检查依赖
ldd /opt/inference-test-app/InferenceApp

# 重新安装依赖
sudo apt-get install -f

# 检查权限
ls -la /opt/inference-test-app/InferenceApp
```

### Q3: 推理功能不工作
**解决方法**:
1. 检查模型文件是否存在：
   ```bash
   ls -la /userdata/install/demo_Linux_aarch64/
   ```
2. 检查配置文件：
   ```bash
   cat /etc/inference-test-app/inference.conf
   ```
3. 查看日志文件：
   ```bash
   tail -f /var/log/inference-test-app/app.log
   ```

### Q4: 图片保存失败
**解决方法**:
```bash
# 检查保存目录权限
ls -la /opt/inference-test-app/saved_images/

# 修复权限
sudo chmod 755 /opt/inference-test-app/saved_images/
```

### Q5: 程序运行缓慢
**解决方法**:
1. 检查系统资源使用情况：
   ```bash
   top
   free -h
   df -h
   ```
2. 关闭不必要的程序
3. 检查模型文件大小和系统内存

---

## 🗑️ 卸载方法

### 完全卸载
```bash
# 卸载软件包
sudo dpkg -r inference-test-app

# 删除配置文件（可选）
sudo rm -rf /etc/inference-test-app/

# 删除日志文件（可选）
sudo rm -rf /var/log/inference-test-app/

# 删除用户数据（可选，请谨慎操作）
sudo rm -rf /opt/inference-test-app/
```

### 保留配置的卸载
```bash
# 只卸载程序，保留配置
sudo dpkg -r inference-test-app

# 配置文件会保留在 /etc/inference-test-app/
```

### 清理残留文件
```bash
# 清理桌面快捷方式
sudo rm -f /usr/share/applications/inference-test-app.desktop

# 清理命令行快捷方式
sudo rm -f /usr/local/bin/inference-test-app

# 从video组移除用户（可选）
sudo gpasswd -d $USER video
```

---

## 📞 技术支持

### 调试信息
- **控制台输出**: 程序运行时的调试信息会显示在终端
- **系统日志**: `/var/log/syslog`

### 查看调试信息
```bash
# 运行程序并查看输出
./InferenceApp

# 或者重定向到文件
./InferenceApp 2>&1 | tee debug.log

# 查看系统日志
journalctl -f | grep InferenceApp
```

### 收集诊断信息
```bash
# 运行环境检查脚本（如果提供）
./check_environment.sh

# 收集系统信息
uname -a
lsb_release -a
dpkg -l | grep inference-test-app

# 运行程序并保存输出
./InferenceApp 2>&1 | tee debug_output.log
```

### 联系支持
如果遇到问题，请提供以下信息：
1. 系统信息（`uname -a`）
2. 错误信息截图
3. 相关日志文件
4. 问题复现步骤

---

## 📝 版本信息

- **软件版本**: 1.0.0
- **支持架构**: arm64
- **兼容系统**: Debian 10+, Ubuntu 18.04+
- **更新日期**: 2024年

---

## ⚠️ 重要提示

1. **权限要求**: 安装需要管理员权限，使用需要摄像头权限
2. **数据安全**: 程序启动时会自动清空旧的图片文件
3. **离线运行**: 软件可以完全离线运行，不需要网络连接
4. **备份建议**: 重要的推理结果请及时备份
5. **更新方式**: 软件更新需要重新安装.deb包

---

*本手册适用于模型推理测试系统 v1.0.0，如有疑问请联系技术支持。*
