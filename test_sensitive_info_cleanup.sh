#!/bin/bash
# 测试敏感信息清理效果

echo "=== 测试敏感信息清理效果 ==="

echo "1. 检查医疗相关词汇:"
medical_terms="症状|医疗|药品|药物|治疗|诊断|病|疾病|健胃|感冒|消食|润喉|胃肠炎|泄泻|止痛|消炎|维生素"
medical_count=$(grep -r "$medical_terms" *.cpp *.h config/ 2>/dev/null | wc -l || echo "0")
echo "  剩余医疗相关词汇: $medical_count 处"

if [ "$medical_count" -eq 0 ]; then
    echo "  ✓ 所有医疗相关词汇已清理"
else
    echo "  ! 仍有医疗相关词汇残留"
    echo "  残留内容:"
    grep -rn "$medical_terms" *.cpp *.h config/ 2>/dev/null | head -10
fi

echo ""
echo "2. 检查配置文件中的敏感信息:"
config_sensitive=$(grep -r "药品\|药物\|症状\|治疗\|诊断\|疾病" config/ 2>/dev/null | wc -l || echo "0")
echo "  配置文件中敏感信息: $config_sensitive 处"

if [ "$config_sensitive" -eq 0 ]; then
    echo "  ✓ 配置文件中的敏感信息已清理"
else
    echo "  ! 配置文件中仍有敏感信息"
    grep -rn "药品\|药物\|症状\|治疗\|诊断\|疾病" config/ 2>/dev/null
fi

echo ""
echo "3. 检查题目文件中的敏感内容:"
if [ -f "config/questions.json" ]; then
    question_sensitive=$(grep -c "药品\|药物\|治疗\|诊断\|疾病\|健胃\|感冒\|消食\|润喉\|胃肠炎\|泄泻" config/questions.json || echo "0")
    echo "  题目文件中敏感内容: $question_sensitive 处"
    
    if [ "$question_sensitive" -eq 0 ]; then
        echo "  ✓ 题目文件中的敏感内容已清理"
    else
        echo "  ! 题目文件中仍有敏感内容"
    fi
else
    echo "  ! 题目文件不存在"
fi

echo ""
echo "4. 检查源代码中的敏感字段名:"
field_sensitive=$(grep -r "symptomPlaceholder\|medicineType\|expectedSymptoms\|detectedMedicine\|medicineImage\|dosageInstructions" *.cpp *.h 2>/dev/null | wc -l || echo "0")
echo "  源代码中敏感字段名: $field_sensitive 处"

if [ "$field_sensitive" -eq 0 ]; then
    echo "  ✓ 源代码中的敏感字段名已清理"
else
    echo "  ! 源代码中仍有敏感字段名"
    grep -rn "symptomPlaceholder\|medicineType\|expectedSymptoms\|detectedMedicine\|medicineImage\|dosageInstructions" *.cpp *.h 2>/dev/null | head -5
fi

echo ""
echo "5. 检查默认任务和题目:"
default_sensitive=$(grep -r "感冒\|止痛\|消炎\|胃药\|维生素\|健胃\|消食" *.cpp *.h 2>/dev/null | wc -l || echo "0")
echo "  默认任务和题目中敏感内容: $default_sensitive 处"

if [ "$default_sensitive" -eq 0 ]; then
    echo "  ✓ 默认任务和题目中的敏感内容已清理"
else
    echo "  ! 默认任务和题目中仍有敏感内容"
    grep -rn "感冒\|止痛\|消炎\|胃药\|维生素\|健胃\|消食" *.cpp *.h 2>/dev/null | head -5
fi

echo ""
echo "=== 清理效果总结 ==="
total_sensitive=$(grep -r "$medical_terms\|感冒\|止痛\|消炎\|胃药\|维生素\|健胃\|消食" *.cpp *.h config/ 2>/dev/null | wc -l || echo "0")
echo "项目中剩余敏感信息总数: $total_sensitive"

if [ "$total_sensitive" -eq 0 ]; then
    echo "✅ 所有敏感信息已成功清理"
else
    echo "⚠️  仍有部分敏感信息需要清理"
fi

echo ""
echo "=== 预期效果 ==="
echo "现在项目应该："
echo "- 不包含任何医疗、药品相关的敏感词汇"
echo "- 使用通用的物品、特征、属性等中性词汇"
echo "- 配置文件和题目文件都已去敏感化"
echo "- 不会泄露任何医疗相关的敏感信息"

echo ""
echo "=== 测试完成 ==="
