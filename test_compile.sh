#!/bin/bash
# 简单的编译测试脚本

echo "开始编译测试..."

# 检查必要的文件是否存在
echo "检查文件..."
files_to_check=(
    "InferenceApp.pro"
    "main.cpp"
    "mainwindow.h"
    "mainwindow.cpp"
    "modelmanager.h"
    "modelmanager.cpp"
    "taskpage.h"
    "taskpage.cpp"
)

for file in "${files_to_check[@]}"; do
    if [ ! -f "$file" ]; then
        echo "错误: 文件 $file 不存在"
        exit 1
    else
        echo "✓ $file 存在"
    fi
done

echo "所有必要文件都存在"

# 尝试运行qmake
echo "运行qmake..."
if qmake InferenceApp.pro; then
    echo "✓ qmake 成功"
else
    echo "✗ qmake 失败"
    exit 1
fi

echo "编译测试完成"
